/* tslint:disable */
/* eslint-disable */
import { QwProductType } from '../models/qw-product-type';

/**
 * [schema: CertificateMetadata] Essential metadata extracted from structured certificate text.
 */
export interface QwCertificateMetadata {

  /**
   * List of all batch numbers found in the certificate
   */
  batch_numbers: Array<string>;

  /**
   * List of all heat numbers found in the certificate (if any)
   */
  heat_numbers: Array<string>;

  /**
   * Type of product this certificate covers
   */
  product_type: QwProductType;
}
