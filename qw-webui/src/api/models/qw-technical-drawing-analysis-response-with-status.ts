/* tslint:disable */
/* eslint-disable */
import { QwTechnicalDrawingAnalysisResult } from '../models/qw-technical-drawing-analysis-result';

/**
 * [schema: TechnicalDrawingAnalysisResponseWithStatus] Response model for technical drawing analysis with status information.
 */
export interface QwTechnicalDrawingAnalysisResponseWithStatus {
  data: (QwTechnicalDrawingAnalysisResult | null);
  message: (string | null);
  status: (string | null);
}
