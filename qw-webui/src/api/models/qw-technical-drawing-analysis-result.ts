/* tslint:disable */
/* eslint-disable */
import { QwNormalizedPmiBlockAnalysisResult } from '../models/qw-normalized-pmi-block-analysis-result';
import { QwPmiMetadata } from '../models/qw-pmi-metadata';

/**
 * [schema: TechnicalDrawingAnalysisResult] Result model for technical drawing analysis with per-page metadata support.
 */
export interface QwTechnicalDrawingAnalysisResult {
  metadata?: {
[key: string]: (QwPmiMetadata | null);
};
  results?: {
[key: string]: Array<QwNormalizedPmiBlockAnalysisResult>;
};
}
