/* tslint:disable */
/* eslint-disable */

/**
 * [schema: HeaderDetails] Header/batch details with foreign key references.
 * Treated like a database table - always includes batch_number and heat_number.
 */
export interface QwHeaderDetails {

  /**
   * Batch/item identifier - acts as primary key
   */
  batch_number: string;

  /**
   * Heat/Smelt number - acts as secondary key
   */
  heat_number?: (string | null);

  /**
   * Length in mm
   */
  length_mm?: (number | string | null);

  /**
   * Manufacturer name (capitalize first letter only)
   */
  manufacturer: string;

  /**
   * Steel grade (e.g., S355J2+N)
   */
  material_grade?: (string | null);

  /**
   * Product description (e.g., Hot Rolled Heavy Plate)
   */
  product_description?: (string | null);

  /**
   * Thickness in mm
   */
  thickness_mm?: (number | string | null);

  /**
   * Weight in kg
   */
  weight_kg?: (number | string | null);

  /**
   * Width in mm
   */
  width_mm?: (number | string | null);
}
