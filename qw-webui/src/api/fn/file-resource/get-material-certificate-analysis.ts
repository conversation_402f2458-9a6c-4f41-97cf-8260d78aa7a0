/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { QwMaterialCertificateAnalysisResponseWithStatus } from '../../models/qw-material-certificate-analysis-response-with-status';

export interface GetMaterialCertificateAnalysis$Params {
  fileResourceRevisionId: number;
}

export function getMaterialCertificateAnalysis(http: HttpClient, rootUrl: string, params: GetMaterialCertificateAnalysis$Params, context?: HttpContext): Observable<StrictHttpResponse<QwMaterialCertificateAnalysisResponseWithStatus>> {
  const rb = new RequestBuilder(rootUrl, getMaterialCertificateAnalysis.PATH, 'get');
  if (params) {
    rb.path('fileResourceRevisionId', params.fileResourceRevisionId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<QwMaterialCertificateAnalysisResponseWithStatus>;
    })
  );
}

getMaterialCertificateAnalysis.PATH = '/api/v1/file-resource/material-certificate/revision/{fileResourceRevisionId}/analysis';
