/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { QwTechnicalDrawingAnalysisResponseWithStatus } from '../../models/qw-technical-drawing-analysis-response-with-status';

export interface GetDrawingRevisionAnalysis$Params {
  fileResourceRevisionId: number;
  pageIndex?: (number | null);
}

export function getDrawingRevisionAnalysis(http: HttpClient, rootUrl: string, params: GetDrawingRevisionAnalysis$Params, context?: HttpContext): Observable<StrictHttpResponse<QwTechnicalDrawingAnalysisResponseWithStatus>> {
  const rb = new RequestBuilder(rootUrl, getDrawingRevisionAnalysis.PATH, 'get');
  if (params) {
    rb.path('fileResourceRevisionId', params.fileResourceRevisionId, {});
    rb.query('pageIndex', params.pageIndex, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<QwTechnicalDrawingAnalysisResponseWithStatus>;
    })
  );
}

getDrawingRevisionAnalysis.PATH = '/api/v1/file-resource/drawing/revision/{fileResourceRevisionId}/analysis';
