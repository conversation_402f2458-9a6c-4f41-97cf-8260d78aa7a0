/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { addDrawingDiscussion } from '../fn/file-resource/add-drawing-discussion';
import { AddDrawingDiscussion$Params } from '../fn/file-resource/add-drawing-discussion';
import { addFileResourceRevision } from '../fn/file-resource/add-file-resource-revision';
import { AddFileResourceRevision$Params } from '../fn/file-resource/add-file-resource-revision';
import { deleteFileResources } from '../fn/file-resource/delete-file-resources';
import { DeleteFileResources$Params } from '../fn/file-resource/delete-file-resources';
import { QwDeleteFileResourcesOutput } from '../models/qw-delete-file-resources-output';
import { QwDrawingDiscussionView } from '../models/qw-drawing-discussion-view';
import { QwFileResourceRenameOutput } from '../models/qw-file-resource-rename-output';
import { QwFileResourceView } from '../models/qw-file-resource-view';
import { getDrawingDiscussion } from '../fn/file-resource/get-drawing-discussion';
import { GetDrawingDiscussion$Params } from '../fn/file-resource/get-drawing-discussion';
import { getDrawingRevisionAnalysis } from '../fn/file-resource/get-drawing-revision-analysis';
import { GetDrawingRevisionAnalysis$Params } from '../fn/file-resource/get-drawing-revision-analysis';
import { getDrawingRevisionPageImage$Png } from '../fn/file-resource/get-drawing-revision-page-image-png';
import { GetDrawingRevisionPageImage$Png$Params } from '../fn/file-resource/get-drawing-revision-page-image-png';
import { getDrawingRevisionPageImage$Webp } from '../fn/file-resource/get-drawing-revision-page-image-webp';
import { GetDrawingRevisionPageImage$Webp$Params } from '../fn/file-resource/get-drawing-revision-page-image-webp';
import { getDrawingRevisionPageTiles } from '../fn/file-resource/get-drawing-revision-page-tiles';
import { GetDrawingRevisionPageTiles$Params } from '../fn/file-resource/get-drawing-revision-page-tiles';
import { QwGetDrawingRevisionPageTilesOutput } from '../models/qw-get-drawing-revision-page-tiles-output';
import { getFileResource } from '../fn/file-resource/get-file-resource';
import { GetFileResource$Params } from '../fn/file-resource/get-file-resource';
import { getFileResourceDownload } from '../fn/file-resource/get-file-resource-download';
import { GetFileResourceDownload$Params } from '../fn/file-resource/get-file-resource-download';
import { getFileResourceRevision } from '../fn/file-resource/get-file-resource-revision';
import { GetFileResourceRevision$Params } from '../fn/file-resource/get-file-resource-revision';
import { getMaterialCertificateAnalysis } from '../fn/file-resource/get-material-certificate-analysis';
import { GetMaterialCertificateAnalysis$Params } from '../fn/file-resource/get-material-certificate-analysis';
import { getMaterialCertificatePageImage$Png } from '../fn/file-resource/get-material-certificate-page-image-png';
import { GetMaterialCertificatePageImage$Png$Params } from '../fn/file-resource/get-material-certificate-page-image-png';
import { getMaterialCertificatePageImage$Webp } from '../fn/file-resource/get-material-certificate-page-image-webp';
import { GetMaterialCertificatePageImage$Webp$Params } from '../fn/file-resource/get-material-certificate-page-image-webp';
import { linkFileToMaterial } from '../fn/file-resource/link-file-to-material';
import { LinkFileToMaterial$Params } from '../fn/file-resource/link-file-to-material';
import { QwLinkFileToMaterialOutput } from '../models/qw-link-file-to-material-output';
import { listDrawingDiscussions } from '../fn/file-resource/list-drawing-discussions';
import { ListDrawingDiscussions$Params } from '../fn/file-resource/list-drawing-discussions';
import { QwListDrawingDiscussionsOutput } from '../models/qw-list-drawing-discussions-output';
import { listFileResources } from '../fn/file-resource/list-file-resources';
import { ListFileResources$Params } from '../fn/file-resource/list-file-resources';
import { listFileResourcesMetadata } from '../fn/file-resource/list-file-resources-metadata';
import { ListFileResourcesMetadata$Params } from '../fn/file-resource/list-file-resources-metadata';
import { QwListFileResourcesMetadataOutput } from '../models/qw-list-file-resources-metadata-output';
import { QwListFileResourcesOutput } from '../models/qw-list-file-resources-output';
import { QwMaterialCertificateAnalysisResponseWithStatus } from '../models/qw-material-certificate-analysis-response-with-status';
import { QwNewDrawingDiscussionOutput } from '../models/qw-new-drawing-discussion-output';
import { QwNewFileResourceRevisionOutput } from '../models/qw-new-file-resource-revision-output';
import { renameFileResource } from '../fn/file-resource/rename-file-resource';
import { RenameFileResource$Params } from '../fn/file-resource/rename-file-resource';
import { setDrawingDiscussionStatus } from '../fn/file-resource/set-drawing-discussion-status';
import { SetDrawingDiscussionStatus$Params } from '../fn/file-resource/set-drawing-discussion-status';
import { QwSetDrawingDiscussionStatusOutput } from '../models/qw-set-drawing-discussion-status-output';
import { QwTechnicalDrawingAnalysisResponseWithStatus } from '../models/qw-technical-drawing-analysis-response-with-status';
import { toggleFileResourceRevisionAccess } from '../fn/file-resource/toggle-file-resource-revision-access';
import { ToggleFileResourceRevisionAccess$Params } from '../fn/file-resource/toggle-file-resource-revision-access';
import { QwToggleFileResourceRevisionAccessOutput } from '../models/qw-toggle-file-resource-revision-access-output';
import { unlinkFileFromMaterial } from '../fn/file-resource/unlink-file-from-material';
import { UnlinkFileFromMaterial$Params } from '../fn/file-resource/unlink-file-from-material';
import { QwUnlinkFileFromMaterialOutput } from '../models/qw-unlink-file-from-material-output';

@Injectable({ providedIn: 'root' })
export class QwFileResourceService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `addFileResourceRevision()` */
  static readonly AddFileResourceRevisionPath = '/api/v1/file-resource/revision';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addFileResourceRevision()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  addFileResourceRevision$Response(params: AddFileResourceRevision$Params, context?: HttpContext): Observable<StrictHttpResponse<QwNewFileResourceRevisionOutput>> {
    return addFileResourceRevision(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addFileResourceRevision$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  addFileResourceRevision(params: AddFileResourceRevision$Params, context?: HttpContext): Observable<QwNewFileResourceRevisionOutput> {
    return this.addFileResourceRevision$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwNewFileResourceRevisionOutput>): QwNewFileResourceRevisionOutput => r.body)
    );
  }

  /** Path part for operation `getFileResource()` */
  static readonly GetFileResourcePath = '/api/v1/file-resource/{fileResourceId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFileResource()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFileResource$Response(params: GetFileResource$Params, context?: HttpContext): Observable<StrictHttpResponse<QwFileResourceView>> {
    return getFileResource(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFileResource$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFileResource(params: GetFileResource$Params, context?: HttpContext): Observable<QwFileResourceView> {
    return this.getFileResource$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwFileResourceView>): QwFileResourceView => r.body)
    );
  }

  /** Path part for operation `getFileResourceRevision()` */
  static readonly GetFileResourceRevisionPath = '/api/v1/file-resource/revision/{fileResourceRevisionId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFileResourceRevision()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFileResourceRevision$Response(params: GetFileResourceRevision$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return getFileResourceRevision(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFileResourceRevision$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFileResourceRevision(params: GetFileResourceRevision$Params, context?: HttpContext): Observable<any> {
    return this.getFileResourceRevision$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `listFileResources()` */
  static readonly ListFileResourcesPath = '/api/v1/file-resources';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `listFileResources()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  listFileResources$Response(params: ListFileResources$Params, context?: HttpContext): Observable<StrictHttpResponse<QwListFileResourcesOutput>> {
    return listFileResources(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `listFileResources$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  listFileResources(params: ListFileResources$Params, context?: HttpContext): Observable<QwListFileResourcesOutput> {
    return this.listFileResources$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwListFileResourcesOutput>): QwListFileResourcesOutput => r.body)
    );
  }

  /** Path part for operation `listFileResourcesMetadata()` */
  static readonly ListFileResourcesMetadataPath = '/api/v1/file-resources-metadata';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `listFileResourcesMetadata()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  listFileResourcesMetadata$Response(params: ListFileResourcesMetadata$Params, context?: HttpContext): Observable<StrictHttpResponse<QwListFileResourcesMetadataOutput>> {
    return listFileResourcesMetadata(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `listFileResourcesMetadata$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  listFileResourcesMetadata(params: ListFileResourcesMetadata$Params, context?: HttpContext): Observable<QwListFileResourcesMetadataOutput> {
    return this.listFileResourcesMetadata$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwListFileResourcesMetadataOutput>): QwListFileResourcesMetadataOutput => r.body)
    );
  }

  /** Path part for operation `getDrawingRevisionAnalysis()` */
  static readonly GetDrawingRevisionAnalysisPath = '/api/v1/file-resource/drawing/revision/{fileResourceRevisionId}/analysis';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getDrawingRevisionAnalysis()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDrawingRevisionAnalysis$Response(params: GetDrawingRevisionAnalysis$Params, context?: HttpContext): Observable<StrictHttpResponse<QwTechnicalDrawingAnalysisResponseWithStatus>> {
    return getDrawingRevisionAnalysis(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getDrawingRevisionAnalysis$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDrawingRevisionAnalysis(params: GetDrawingRevisionAnalysis$Params, context?: HttpContext): Observable<QwTechnicalDrawingAnalysisResponseWithStatus> {
    return this.getDrawingRevisionAnalysis$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwTechnicalDrawingAnalysisResponseWithStatus>): QwTechnicalDrawingAnalysisResponseWithStatus => r.body)
    );
  }

  /** Path part for operation `getDrawingRevisionPageImage()` */
  static readonly GetDrawingRevisionPageImagePath = '/api/v1/file-resource/drawing/revision/{fileResourceRevisionId}/page/{pageIndex}/image';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getDrawingRevisionPageImage$Png()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDrawingRevisionPageImage$Png$Response(params: GetDrawingRevisionPageImage$Png$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return getDrawingRevisionPageImage$Png(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getDrawingRevisionPageImage$Png$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDrawingRevisionPageImage$Png(params: GetDrawingRevisionPageImage$Png$Params, context?: HttpContext): Observable<any> {
    return this.getDrawingRevisionPageImage$Png$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getDrawingRevisionPageImage$Webp()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDrawingRevisionPageImage$Webp$Response(params: GetDrawingRevisionPageImage$Webp$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return getDrawingRevisionPageImage$Webp(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getDrawingRevisionPageImage$Webp$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDrawingRevisionPageImage$Webp(params: GetDrawingRevisionPageImage$Webp$Params, context?: HttpContext): Observable<any> {
    return this.getDrawingRevisionPageImage$Webp$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `getDrawingRevisionPageTiles()` */
  static readonly GetDrawingRevisionPageTilesPath = '/api/v1/file-resource/drawing/revision/{fileResourceRevisionId}/page/{pageIndex}/tiles';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getDrawingRevisionPageTiles()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDrawingRevisionPageTiles$Response(params: GetDrawingRevisionPageTiles$Params, context?: HttpContext): Observable<StrictHttpResponse<QwGetDrawingRevisionPageTilesOutput>> {
    return getDrawingRevisionPageTiles(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getDrawingRevisionPageTiles$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDrawingRevisionPageTiles(params: GetDrawingRevisionPageTiles$Params, context?: HttpContext): Observable<QwGetDrawingRevisionPageTilesOutput> {
    return this.getDrawingRevisionPageTiles$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwGetDrawingRevisionPageTilesOutput>): QwGetDrawingRevisionPageTilesOutput => r.body)
    );
  }

  /** Path part for operation `getMaterialCertificateAnalysis()` */
  static readonly GetMaterialCertificateAnalysisPath = '/api/v1/file-resource/material-certificate/revision/{fileResourceRevisionId}/analysis';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMaterialCertificateAnalysis()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMaterialCertificateAnalysis$Response(params: GetMaterialCertificateAnalysis$Params, context?: HttpContext): Observable<StrictHttpResponse<QwMaterialCertificateAnalysisResponseWithStatus>> {
    return getMaterialCertificateAnalysis(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMaterialCertificateAnalysis$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMaterialCertificateAnalysis(params: GetMaterialCertificateAnalysis$Params, context?: HttpContext): Observable<QwMaterialCertificateAnalysisResponseWithStatus> {
    return this.getMaterialCertificateAnalysis$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwMaterialCertificateAnalysisResponseWithStatus>): QwMaterialCertificateAnalysisResponseWithStatus => r.body)
    );
  }

  /** Path part for operation `getMaterialCertificatePageImage()` */
  static readonly GetMaterialCertificatePageImagePath = '/api/v1/file-resource/material-certificate/revision/{fileResourceRevisionId}/page/{pageIndex}/image';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMaterialCertificatePageImage$Png()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMaterialCertificatePageImage$Png$Response(params: GetMaterialCertificatePageImage$Png$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return getMaterialCertificatePageImage$Png(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMaterialCertificatePageImage$Png$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMaterialCertificatePageImage$Png(params: GetMaterialCertificatePageImage$Png$Params, context?: HttpContext): Observable<any> {
    return this.getMaterialCertificatePageImage$Png$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMaterialCertificatePageImage$Webp()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMaterialCertificatePageImage$Webp$Response(params: GetMaterialCertificatePageImage$Webp$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return getMaterialCertificatePageImage$Webp(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMaterialCertificatePageImage$Webp$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMaterialCertificatePageImage$Webp(params: GetMaterialCertificatePageImage$Webp$Params, context?: HttpContext): Observable<any> {
    return this.getMaterialCertificatePageImage$Webp$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `addDrawingDiscussion()` */
  static readonly AddDrawingDiscussionPath = '/api/v1/file-resource/drawing-discussion';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addDrawingDiscussion()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addDrawingDiscussion$Response(params: AddDrawingDiscussion$Params, context?: HttpContext): Observable<StrictHttpResponse<QwNewDrawingDiscussionOutput>> {
    return addDrawingDiscussion(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addDrawingDiscussion$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addDrawingDiscussion(params: AddDrawingDiscussion$Params, context?: HttpContext): Observable<QwNewDrawingDiscussionOutput> {
    return this.addDrawingDiscussion$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwNewDrawingDiscussionOutput>): QwNewDrawingDiscussionOutput => r.body)
    );
  }

  /** Path part for operation `getDrawingDiscussion()` */
  static readonly GetDrawingDiscussionPath = '/api/v1/file-resource/drawing-discussion/{drawingDiscussionId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getDrawingDiscussion()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDrawingDiscussion$Response(params: GetDrawingDiscussion$Params, context?: HttpContext): Observable<StrictHttpResponse<QwDrawingDiscussionView>> {
    return getDrawingDiscussion(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getDrawingDiscussion$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDrawingDiscussion(params: GetDrawingDiscussion$Params, context?: HttpContext): Observable<QwDrawingDiscussionView> {
    return this.getDrawingDiscussion$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwDrawingDiscussionView>): QwDrawingDiscussionView => r.body)
    );
  }

  /** Path part for operation `setDrawingDiscussionStatus()` */
  static readonly SetDrawingDiscussionStatusPath = '/api/v1/file-resource/drawing-discussion/{drawingDiscussionId}/status';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `setDrawingDiscussionStatus()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  setDrawingDiscussionStatus$Response(params: SetDrawingDiscussionStatus$Params, context?: HttpContext): Observable<StrictHttpResponse<QwSetDrawingDiscussionStatusOutput>> {
    return setDrawingDiscussionStatus(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `setDrawingDiscussionStatus$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  setDrawingDiscussionStatus(params: SetDrawingDiscussionStatus$Params, context?: HttpContext): Observable<QwSetDrawingDiscussionStatusOutput> {
    return this.setDrawingDiscussionStatus$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwSetDrawingDiscussionStatusOutput>): QwSetDrawingDiscussionStatusOutput => r.body)
    );
  }

  /** Path part for operation `listDrawingDiscussions()` */
  static readonly ListDrawingDiscussionsPath = '/api/v1/file-resource/drawing-discussions';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `listDrawingDiscussions()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  listDrawingDiscussions$Response(params: ListDrawingDiscussions$Params, context?: HttpContext): Observable<StrictHttpResponse<QwListDrawingDiscussionsOutput>> {
    return listDrawingDiscussions(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `listDrawingDiscussions$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  listDrawingDiscussions(params: ListDrawingDiscussions$Params, context?: HttpContext): Observable<QwListDrawingDiscussionsOutput> {
    return this.listDrawingDiscussions$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwListDrawingDiscussionsOutput>): QwListDrawingDiscussionsOutput => r.body)
    );
  }

  /** Path part for operation `renameFileResource()` */
  static readonly RenameFileResourcePath = '/api/v1/file-resource/{fileResourceId}/rename';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `renameFileResource()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  renameFileResource$Response(params: RenameFileResource$Params, context?: HttpContext): Observable<StrictHttpResponse<QwFileResourceRenameOutput>> {
    return renameFileResource(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `renameFileResource$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  renameFileResource(params: RenameFileResource$Params, context?: HttpContext): Observable<QwFileResourceRenameOutput> {
    return this.renameFileResource$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwFileResourceRenameOutput>): QwFileResourceRenameOutput => r.body)
    );
  }

  /** Path part for operation `deleteFileResources()` */
  static readonly DeleteFileResourcesPath = '/api/v1/file-resources/delete';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteFileResources()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deleteFileResources$Response(params: DeleteFileResources$Params, context?: HttpContext): Observable<StrictHttpResponse<QwDeleteFileResourcesOutput>> {
    return deleteFileResources(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteFileResources$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deleteFileResources(params: DeleteFileResources$Params, context?: HttpContext): Observable<QwDeleteFileResourcesOutput> {
    return this.deleteFileResources$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwDeleteFileResourcesOutput>): QwDeleteFileResourcesOutput => r.body)
    );
  }

  /** Path part for operation `getFileResourceDownload()` */
  static readonly GetFileResourceDownloadPath = '/api/v1/file-resource/revision/{fileResourceRevisionId}/download';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFileResourceDownload()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFileResourceDownload$Response(params: GetFileResourceDownload$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return getFileResourceDownload(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFileResourceDownload$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFileResourceDownload(params: GetFileResourceDownload$Params, context?: HttpContext): Observable<any> {
    return this.getFileResourceDownload$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `toggleFileResourceRevisionAccess()` */
  static readonly ToggleFileResourceRevisionAccessPath = '/api/v1/file-resource/revision/share';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `toggleFileResourceRevisionAccess()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  toggleFileResourceRevisionAccess$Response(params: ToggleFileResourceRevisionAccess$Params, context?: HttpContext): Observable<StrictHttpResponse<QwToggleFileResourceRevisionAccessOutput>> {
    return toggleFileResourceRevisionAccess(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `toggleFileResourceRevisionAccess$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  toggleFileResourceRevisionAccess(params: ToggleFileResourceRevisionAccess$Params, context?: HttpContext): Observable<QwToggleFileResourceRevisionAccessOutput> {
    return this.toggleFileResourceRevisionAccess$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwToggleFileResourceRevisionAccessOutput>): QwToggleFileResourceRevisionAccessOutput => r.body)
    );
  }

  /** Path part for operation `linkFileToMaterial()` */
  static readonly LinkFileToMaterialPath = '/api/v1/file-resource-revision/link-to-material';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `linkFileToMaterial()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  linkFileToMaterial$Response(params: LinkFileToMaterial$Params, context?: HttpContext): Observable<StrictHttpResponse<QwLinkFileToMaterialOutput>> {
    return linkFileToMaterial(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `linkFileToMaterial$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  linkFileToMaterial(params: LinkFileToMaterial$Params, context?: HttpContext): Observable<QwLinkFileToMaterialOutput> {
    return this.linkFileToMaterial$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwLinkFileToMaterialOutput>): QwLinkFileToMaterialOutput => r.body)
    );
  }

  /** Path part for operation `unlinkFileFromMaterial()` */
  static readonly UnlinkFileFromMaterialPath = '/api/v1/file-resource-revision/unlink-from-material';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `unlinkFileFromMaterial()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  unlinkFileFromMaterial$Response(params: UnlinkFileFromMaterial$Params, context?: HttpContext): Observable<StrictHttpResponse<QwUnlinkFileFromMaterialOutput>> {
    return unlinkFileFromMaterial(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `unlinkFileFromMaterial$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  unlinkFileFromMaterial(params: UnlinkFileFromMaterial$Params, context?: HttpContext): Observable<QwUnlinkFileFromMaterialOutput> {
    return this.unlinkFileFromMaterial$Response(params, context).pipe(
      map((r: StrictHttpResponse<QwUnlinkFileFromMaterialOutput>): QwUnlinkFileFromMaterialOutput => r.body)
    );
  }

}
