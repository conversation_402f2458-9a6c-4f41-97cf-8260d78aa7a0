import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Date<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>plateOutlet,
} from "@angular/common"
import { Component, DestroyRef, inject, Input } from "@angular/core"
import { takeUntilDestroyed } from "@angular/core/rxjs-interop"
import { RouterLink, RouterLinkActive, RouterOutlet } from "@angular/router"
import { FaIconComponent } from "@fortawesome/angular-fontawesome"
import {
  faArrowUpFromBracket,
  faBriefcase,
  faCalculator,
  faCalendarCheck,
  faCircleCheck,
  faCircleExclamation,
  faClock,
  faCompassDrafting,
  faCube,
  faFolderOpen,
  faHashtag,
  faHourglassStart,
  faListUl,
  faMessage,
  faRulerCombined,
  faTruck,
} from "@fortawesome/free-solid-svg-icons"
import { AppRoutes } from "@qw-core/constants/app-routes"
import { AuthService } from "@qw-core/services/auth.service"
import { CopyCurrentUrlService } from "@qw-core/services/copy-current-url.service"
import { ScreenSizeService } from "@qw-core/services/screen-size.service"
import {
  PolDataService,
  PurchaseOrderLineData,
} from "@qw-shared-services/pol-data.service"
import {
  PolState,
  PolStateService,
} from "@qw-shared-services/pol-state.service"
import { TenantDataService } from "@qw-shared-services/tenant-data.service"
import {
  BehaviorSubject,
  combineLatest,
  filter,
  map,
  Observable,
  switchMap,
} from "rxjs"

import { QwOrderType } from "@qw-api/models"

interface PolDetailViewModel {
  viewMode: string
  isMobile: boolean
  isPortrait: boolean
  polState: PolState
}

@Component({
  selector: "qw-purchase-order-line",
  templateUrl: "./pol-detail.component.html",
  standalone: true,
  imports: [
    NgIf,
    NgTemplateOutlet,
    RouterOutlet,
    FaIconComponent,
    RouterLinkActive,
    RouterLink,
    NgClass,
    AsyncPipe,
    DatePipe,
  ],
})
export class PolDetailComponent {
  private polId$ = new BehaviorSubject<number | null>(null)

  @Input() set polId(value: string) {
    if (value) {
      this.polId$.next(+value)
    }
  }

  constructor(
    private authService: AuthService,
    private destroyRef: DestroyRef,
    private polDataService: PolDataService,
    private polStateService: PolStateService,
    private screenSizeService: ScreenSizeService,
    private tenantDataService: TenantDataService,
  ) {
    combineLatest([this.polId$.pipe(filter((id): id is number => id !== null))])
      .pipe(
        switchMap(([polId]) => {
          if (!polId) return []
          return this.polDataService.getPolDataByPolId(polId).pipe(
            filter((data): data is PurchaseOrderLineData => !!data),
            map((data) => {
              const tenantId = this.authService.getCurrentTenantId()
              return {
                material: data.material,
                purchaseOrderLine: data.pol,
                purchaseOrder: data.po,
                tenantId,
                isSupplier: data.supplier?.id === tenantId,
                supplier: data.supplier,
              }
            }),
          )
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((state) => {
        if (state) {
          this.polStateService.updateState(state)
        }
      })
  }

  vm$: Observable<PolDetailViewModel> = combineLatest({
    viewMode: this.tenantDataService.tenantViewMode$,
    isMobile: this.screenSizeService.isMobile$,
    isPortrait: this.screenSizeService.isPortrait$,
    polState: this.polStateService.getPolState(),
  })

  faFolderOpen = faFolderOpen
  faRulerCombined = faRulerCombined
  faCompassDrafting = faCompassDrafting
  faMessage = faMessage
  faListUl = faListUl
  faCalendarCheck = faCalendarCheck
  faCube = faCube
  faCalculator = faCalculator
  faTruck = faTruck
  faBriefcase = faBriefcase
  faHashtag = faHashtag
  faCircleExclamation = faCircleExclamation
  faHourglassStart = faHourglassStart
  faCircleCheck = faCircleCheck
  faClock = faClock

  protected readonly AppRoutes = AppRoutes
  protected readonly QwOrderType = QwOrderType

  // Share link ================================================================
  copyCurrentUrlService = inject(CopyCurrentUrlService)
  faArrowUpFromBracket = faArrowUpFromBracket
  shareLink(): void {
    this.copyCurrentUrlService.shareCurrentPage()
  }
}
