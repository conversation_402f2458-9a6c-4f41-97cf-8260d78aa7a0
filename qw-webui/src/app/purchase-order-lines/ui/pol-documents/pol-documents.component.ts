import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>Outlet,
  SlicePip<PERSON>,
} from "@angular/common"
import {
  Component,
  computed,
  ElementRef,
  signal,
  ViewChild,
} from "@angular/core"
import { takeUntilDestroyed } from "@angular/core/rxjs-interop"
import { ActivatedRoute, Router, RouterLink } from "@angular/router"
import { FaIconComponent } from "@fortawesome/angular-fontawesome"
import { IconProp } from "@fortawesome/fontawesome-svg-core"
import { faEnvelope } from "@fortawesome/free-regular-svg-icons"
import {
  faCloudUpload,
  faDownload,
  faEllipsisVertical,
  faFilter,
  faLink,
  faListCheck,
  faPenToSquare,
  faTrash,
  faUnlink,
  faUser,
  faUserGroup,
} from "@fortawesome/free-solid-svg-icons"
import { AppRoutes } from "@qw-core/constants/app-routes"
import { DragDetectionService } from "@qw-core/services/drag-detection.service"
import { ScreenSizeService } from "@qw-core/services/screen-size.service"
import { DIALOG_MESSAGES } from "@qw-core/ui/dialog-temp/app-messages"
import { QwSwipeDirective } from "@qw-shared-directives/qw-swipe/qw-swipe.directive"
import { QwCapitalizeAllWordsPipe } from "@qw-shared-pipes/qw-capitalize-all-words/qw-capitalize-all-words.pipe"
import { QwInitialsFromWordsPipe } from "@qw-shared-pipes/qw-initials-from-words/qw-initials-from-words.pipe"
import { QwUnderscoreToWordsPipe } from "@qw-shared-pipes/qw-underscore-to-words/qw-underscore-to-words.pipe"
import { AgentActionDispatcherService } from "@qw-shared-services/agent-action-dispatcher.service"
import { AgentService } from "@qw-shared-services/agent.service"
import {
  ContextMenuItem,
  ContextMenuService,
} from "@qw-shared-services/context-menu.service"
import { DialogConfirmationService } from "@qw-shared-services/dialog-confirmation.service"
import { FileResourcesDataService } from "@qw-shared-services/file-resources-data.service"
import { PolDataService } from "@qw-shared-services/pol-data.service"
import {
  PolState,
  PolStateService,
} from "@qw-shared-services/pol-state.service"
import { UserInterfaceService } from "@qw-shared-services/user-interface.service"
import { DocumentUploadBoxComponent } from "@qw-shared-ui/document-upload-box/document-upload-box.component"
import { ViewerFileResourceComponent } from "@qw-shared-ui/viewer-file-resource/viewer-file-resource.component"
import { FILE_LABEL_OPTIONS } from "@qw-shared-utils/qw-get-file-resource-label"
import {
  combineLatest,
  distinctUntilChanged,
  map,
  Observable,
  shareReplay,
  switchMap,
} from "rxjs"

import { DialogUpdateOrderRequirementsComponent } from "../dialog-update-order-requirements/dialog-update-order-requirements.component"
import { RequirementsInputComponent } from "../order-create-inputs/requirements-input/requirements-input.component"

import {
  QwFileResourceLabel,
  QwFileResourceMetadataView,
  QwFileResourceView,
  QwOrderType,
  QwPurchaseOrderInfoView,
} from "@qw-api/models"

type ExtendedFileResource = QwFileResourceView & {
  isInheritedFromMaterial: boolean
}

interface FileResourceState {
  fileResources: ExtendedFileResource[]
  fileResourcesMetadata: QwFileResourceMetadataView[]
  viewedResource: ExtendedFileResource | null
  selectedFileResourceId: number | null
}

interface ViewModel {
  polState: PolState
  fileResourceState: FileResourceState
  isPortrait: boolean
  isMobile: boolean
  isFilesDragging: boolean
}

@Component({
  selector: "qw-pol-documents",
  templateUrl: "./pol-documents.component.html",
  standalone: true,
  providers: [
    QwUnderscoreToWordsPipe,
    QwInitialsFromWordsPipe,
    QwCapitalizeAllWordsPipe,
    DragDetectionService,
  ],
  imports: [
    NgIf,
    NgTemplateOutlet,
    QwSwipeDirective,
    JsonPipe,
    NgClass,
    NgFor,
    RouterLink,
    FaIconComponent,
    DocumentUploadBoxComponent,
    ViewerFileResourceComponent,
    AsyncPipe,
    SlicePipe,
    RequirementsInputComponent,
    DialogUpdateOrderRequirementsComponent,
  ],
})
export class PolDocumentsComponent {
  // Initialization ============================================================
  constructor(
    private actionDispatcher: AgentActionDispatcherService,
    private agentService: AgentService,
    private contextMenuService: ContextMenuService,
    private dialogConfirmationService: DialogConfirmationService,
    private dragDetectionService: DragDetectionService,
    private fileResourcesDataService: FileResourcesDataService,
    private polDataService: PolDataService,
    private polStateService: PolStateService,
    private route: ActivatedRoute,
    private router: Router,
    private screenSizeService: ScreenSizeService,
    private userInterfaceService: UserInterfaceService,
  ) {
    // Register actions with the AgentActionDispatcherService
    this.registerAgentActions()

    // Use combineLatest to properly handle both vm$ and agent visibility
    combineLatest([this.vm$, this.agentService.isVisible$])
      .pipe(takeUntilDestroyed())
      .subscribe((result) => {
        const [viewModel, isVisible] = result
        if (isVisible) {
          // Only pass relevant context to the agent service
          const relevantContext = {
            frontendContextFromOrderDocuments: {
              fileResources: viewModel.fileResourceState.fileResources || [],
              viewedFileResource: viewModel.fileResourceState.viewedResource,
              orderLineId: viewModel.polState.purchaseOrderLine.id,
              // Add structured mapping of revision IDs to file resources
              fileRevisionToFileResourceMap: (
                viewModel.fileResourceState.fileResources || []
              ).reduce(
                (map, resource) => {
                  resource.revisions.forEach((revision) => {
                    map[revision.id] = {
                      fileResourceId: resource.id,
                      fileResourceName: resource.displayName,
                      fileResourceLabel: resource.label,
                    }
                  })
                  return map
                },
                {} as Record<
                  number,
                  {
                    fileResourceId: number
                    fileResourceName: string
                    fileResourceLabel: QwFileResourceLabel
                  }
                >,
              ),
            },
          }
          this.agentService.setContext(relevantContext)
        }
      })
  }

  // Agent action registration =================================================
  private registerAgentActions(): void {
    // Register any actions needed for the cross document auditor agent
    // For now, we don't have any specific actions to register
    // logger.log("Registered agent actions for pol-documents component")
  }

  // Static References==========================================================
  protected readonly AppRoutes = AppRoutes
  protected readonly QwOrderType = QwOrderType

  // Bindings ==================================================================
  faPenToSquare = faPenToSquare
  faUser = faUser
  faUserGroup = faUserGroup
  faTrash = faTrash
  faEnvelope: IconProp = faEnvelope as IconProp
  faEllipsisVertical = faEllipsisVertical
  faFilter = faFilter
  faCloudUpload = faCloudUpload
  faListCheck = faListCheck
  faLink = faLink
  faUnlink = faUnlink

  // Data Streams and State ====================================================

  // Base streams
  private selectedFileResourceId$ = this.route.queryParamMap.pipe(
    map((params) => {
      const frIdParam = params.get("fr")
      return frIdParam && !isNaN(+frIdParam) ? +frIdParam : null
    }),
    distinctUntilChanged(),
    shareReplay(1),
  )

  private polState$ = this.polStateService.getPolState().pipe(shareReplay(1))

  // File resources data - refreshes when polState changes or refresh trigger fires
  private fileResourcesData$ = this.polState$.pipe(
    switchMap((polState) =>
      this.fileResourcesDataService.refreshAllTrigger$.pipe(
        switchMap(() =>
          this.fileResourcesDataService.getFileResourcesByOrderLineAndMaterialId(
            polState.purchaseOrderLine.id,
            polState.purchaseOrderLine?.materialId ?? null,
          ),
        ),
      ),
    ),
    shareReplay(1),
  )

  // File state with view selection - combines data with UI selection
  private allFiles$ = combineLatest([
    this.fileResourcesData$,
    this.selectedFileResourceId$,
  ]).pipe(
    map(([data, selectedFileResourceId]) => {
      if (!data) {
        return {
          fileResources: [],
          fileResourcesMetadata: [],
          viewedResource: null,
          selectedFileResourceId,
        } as FileResourceState
      }

      // Add isInheritedFromMaterial property to files based on service data
      const filesWithFlag: ExtendedFileResource[] = data.fileResources.map(
        (file: QwFileResourceView) => ({
          ...file,
          isInheritedFromMaterial: data.materialFileIds.has(file.id),
        }),
      )

      const sortedFiles = this.sortFileResourcesAlphabetically(filesWithFlag)

      return {
        fileResources: sortedFiles,
        fileResourcesMetadata: data.fileResourcesMetadata,
        viewedResource:
          sortedFiles.find((fr) => fr.id === selectedFileResourceId) ?? null,
        selectedFileResourceId,
      } as FileResourceState
    }),
    shareReplay(1),
  )

  vm$: Observable<ViewModel> = combineLatest([
    this.polState$,
    this.allFiles$,
    this.screenSizeService.isPortrait$,
    this.screenSizeService.isMobile$,
    this.dragDetectionService.filesDragging$,
  ]).pipe(
    map(
      ([
        polState,
        fileResourceState,
        isPortrait,
        isMobile,
        isFilesDragging,
      ]) => ({
        polState,
        fileResourceState,
        isPortrait,
        isMobile,
        isFilesDragging,
      }),
    ),
  )

  private sortFileResourcesAlphabetically(
    files: ExtendedFileResource[],
  ): ExtendedFileResource[] {
    return files.sort((a, b) => a.displayName.localeCompare(b.displayName))
  }

  // Actions ===================================================================
  // download
  private downloadFile(fileRevisionId: number, fileName: string): void {
    this.fileResourcesDataService
      .downloadFileResourceFE(fileRevisionId)
      .subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement("a")
          a.href = url
          a.download = fileName
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        },
        error: (error) => {
          logger.error("Error downloading file", error)
          this.userInterfaceService.showPopup(
            DIALOG_MESSAGES.DOWNLOAD_FILE_FAIL,
          )
        },
      })
  }

  // delete
  deleteFile(fileId: number): void {
    this.fileResourcesDataService.deleteFileResourceFE(fileId).subscribe({
      next: () => {
        this.userInterfaceService.showPopup(DIALOG_MESSAGES.DELETE_SUCCESS)
      },
      error: (error) => {
        logger.error("Error deleting file resource:", error)
        this.userInterfaceService.showPopup(DIALOG_MESSAGES.DELETE_FAIL)
      },
    })
  }

  // rename
  @ViewChild("fileNameInput") fileNameInput?: ElementRef<HTMLInputElement>

  editingFileId = signal<number | null>(null)

  startRenaming(fileId: number): void {
    this.editingFileId.set(fileId)
    setTimeout(() => {
      if (this.fileNameInput) {
        this.fileNameInput.nativeElement.focus()
        this.fileNameInput.nativeElement.select()
      }
    })
  }

  renameFile(file: ExtendedFileResource, event: Event): void {
    const newName = this.getInputValue(event)
    const originalExtension = this.getFileExtension(file.displayName)
    const finalNewName = `${newName}${originalExtension}`

    if (finalNewName && finalNewName !== file.displayName) {
      this.fileResourcesDataService
        .renameFileResourceFE(file.id, finalNewName)
        .subscribe({
          error: (error) => {
            logger.error("Error renaming file resource:", error)
          },
          complete: () => {
            this.editingFileId.set(null)
          },
        })
    } else {
      this.editingFileId.set(null)
    }
  }

  private getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf(".")
    return lastDotIndex === -1 ? "" : fileName.substring(lastDotIndex)
  }

  getInputValue(event: Event): string {
    return (event.target as HTMLInputElement).value
  }

  getFileNameWithoutExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf(".")
    return lastDotIndex === -1 ? fileName : fileName.substring(0, lastDotIndex)
  }

  // Link/Unlink material actions
  linkFileToMaterial(file: ExtendedFileResource, vm: ViewModel): void {
    const materialId = vm.polState.material?.id
    const materialRef = vm.polState.material?.internalReference
    const fileName = file.displayName

    if (!materialId) {
      logger.error("No material ID available for linking")
      return
    }

    this.dialogConfirmationService.show({
      title: $localize`Attach "` + fileName + `" ⇄ "` + materialRef + `"?`,
      message: DIALOG_MESSAGES.ATTACH_MESSAGE,
      confirmText: $localize`Attach`,
      type: "info",
      onConfirm: () => {
        this.fileResourcesDataService
          .linkFileToMaterialFE(file.revisions[0].id, materialId)
          .subscribe({
            next: () => {
              this.userInterfaceService.showPopup(
                DIALOG_MESSAGES.ATTACH_SUCCESS,
              )
            },
            error: (error) => {
              logger.error("Error linking file to material:", error)
              this.userInterfaceService.showPopup(DIALOG_MESSAGES.ERROR)
            },
          })
      },
    })
  }

  unlinkFileFromMaterial(file: ExtendedFileResource, vm: ViewModel): void {
    const materialId = vm.polState.material?.id
    const materialRef = vm.polState.material?.internalReference
    const fileName = file.displayName

    if (!materialId) {
      logger.error("No material ID available for unlinking")
      return
    }

    this.dialogConfirmationService.show({
      title: $localize`Detach "` + fileName + `" ⇄ "` + materialRef + `"?`,
      message: DIALOG_MESSAGES.DETACH_MESSAGE,
      confirmText: $localize`Detach`,
      type: "warning",
      onConfirm: () => {
        this.fileResourcesDataService
          .unlinkFileFromMaterialFE(file.revisions[0].id, materialId)
          .subscribe({
            next: () => {
              this.userInterfaceService.showPopup(
                DIALOG_MESSAGES.DETACH_SUCCESS,
              )
            },
            error: (error) => {
              logger.error("Error unlinking file from material:", error)
              this.userInterfaceService.showPopup(DIALOG_MESSAGES.ERROR)
            },
          })
      },
    })
  }

  // View ======================================================================

  private classifyFileResource = computed(
    () =>
      (
        file: ExtendedFileResource,
        orderRequirements: QwFileResourceLabel[] | undefined,
      ): "requirement" | "other" => {
        // No requirements defined, everything is "other"
        if (!orderRequirements || orderRequirements.length === 0) {
          return "other"
        }

        // If the file's label is directly in requirements, it's a requirement file
        if (orderRequirements.includes(file.label as QwFileResourceLabel)) {
          return "requirement"
        }

        // Otherwise it's an "other" file
        return "other"
      },
  )

  protected requiredFileResources = computed(
    () =>
      (
        fileResources: ExtendedFileResource[],
        orderRequirements: QwFileResourceLabel[] | undefined,
      ): ExtendedFileResource[] => {
        if (!orderRequirements) {
          return []
        }

        // First filter to get requirement-based files
        const requirementFiles = fileResources.filter(
          (file) =>
            this.classifyFileResource()(file, orderRequirements) ===
            "requirement",
        )

        const selectedLabel = this.selectedFileLabel()

        // If no file label selected, return all requirement-based files
        if (selectedLabel === null) {
          return requirementFiles
        }

        // Return files that match the selected file label
        return requirementFiles.filter((file) => file.label === selectedLabel)
      },
  )

  protected readonly fileLabelsForRequirements = computed(
    () =>
      (
        requirements: QwFileResourceLabel[] | undefined,
      ): QwFileResourceLabel[] => {
        if (!requirements || requirements.length === 0) return []

        // Get only file labels from the requirements list
        // A label is considered a file label if it exists in FILE_LABEL_OPTIONS
        const fileLabels = requirements.filter((label) =>
          FILE_LABEL_OPTIONS.some((option) => option.label === label),
        )

        return fileLabels
      },
  )

  // Handle undefined requirements
  protected otherFileResources = computed(
    () =>
      (
        fileResources: ExtendedFileResource[],
        orderRequirements: QwFileResourceLabel[] | undefined,
      ): ExtendedFileResource[] => {
        return fileResources.filter(
          (file) =>
            this.classifyFileResource()(file, orderRequirements) === "other",
        )
      },
  )

  protected selectedFileLabel = signal<QwFileResourceLabel | null>(null)
  isFileLabelFilterApplied = computed(() => this.selectedFileLabel() !== null)

  onSelectFileLabel(fileLabel: QwFileResourceLabel): void {
    this.selectedFileLabel.update((current) =>
      current === fileLabel ? null : fileLabel,
    )
  }

  onDeselectAllFilters() {
    this.selectedFileLabel.set(null)
  }

  fileLabelDisplayName = computed(
    () =>
      (label: QwFileResourceLabel, fullText: boolean = false): string => {
        const option = FILE_LABEL_OPTIONS.find((opt) => opt.label === label)
        const displayName = option?.displayName || String(label)

        // Return full text if requested or if it's short enough
        if (fullText || displayName.length <= 10) {
          return displayName
        }

        // Otherwise return shortened version
        return displayName.slice(0, 10) + "..."
      },
  )

  protected readonly sortedFileLabels = computed(
    () =>
      (
        requirements: QwFileResourceLabel[] | undefined,
      ): QwFileResourceLabel[] => {
        const fileLabels = this.fileLabelsForRequirements()(requirements)
        if (!fileLabels.length) return []

        return [...fileLabels].sort((a, b) => {
          const optionA = FILE_LABEL_OPTIONS.find((opt) => opt.label === a)
          const optionB = FILE_LABEL_OPTIONS.find((opt) => opt.label === b)
          return (optionA?.displayName || "").localeCompare(
            optionB?.displayName || "",
          )
        })
      },
  )

  isFileLabelUnfulfilled = computed(
    () =>
      (
        fileLabel: QwFileResourceLabel,
        fileResources: ExtendedFileResource[],
      ): boolean => {
        // A file label is unfulfilled if no files exist with this label
        return !fileResources.some((file) => file.label === fileLabel)
      },
  )

  isFileShared = computed(
    () =>
      (
        fileResource: QwFileResourceView,
        polState: PolState,
        fileResourcesMetadata: QwFileResourceMetadataView[],
      ): boolean => {
        // Production orders: check owner directly
        if (polState.purchaseOrder.info.type === QwOrderType.Production) {
          return fileResource.owner.id !== polState.tenantId
        }

        // Purchase/sales orders: determine other party
        const orderInfo = polState.purchaseOrder.info as QwPurchaseOrderInfoView
        const otherTenantId =
          polState.tenantId === orderInfo.supplier.id
            ? polState.purchaseOrder.customer.id
            : orderInfo.supplier.id

        // Check metadata for access rights
        const metadata = fileResourcesMetadata.find(
          (frm) => frm.fileResourceId === fileResource.id,
        )

        if (metadata) {
          return metadata.accessRights
            .map((ar) => ar.tenant.id)
            .includes(otherTenantId)
        }

        // If no metadata, file is not shared
        return false
      },
  )

  // Context menu for requirement header
  openContextMenuForMainSection(event: MouseEvent, vm: ViewModel): void {
    event.preventDefault()
    event.stopPropagation()

    const items = this.getMenuItemsForMainSection(vm)
    this.contextMenuService.open(items, null, event)
  }

  private getMenuItemsForMainSection(_vm: ViewModel): ContextMenuItem[] {
    return [
      {
        label: $localize`Upload Files`,
        icon: faCloudUpload,
        action: () => {
          this.triggerFileUpload()
        },
        type: "default",
        visible: () => true,
      },
      {
        label: $localize`Update Requirements`,
        icon: faListCheck,
        action: () => {
          this.updateRequirements()
        },
        type: "default",
        visible: () => true,
      },
    ]
  }

  @ViewChild(DocumentUploadBoxComponent)
  documentUploadBoxComponent!: DocumentUploadBoxComponent

  triggerFileUpload(): void {
    const input = document.createElement("input")
    input.type = "file"
    input.multiple = true
    input.click()
    input.onchange = () => {
      if (input.files) {
        this.documentUploadBoxComponent.selectFilesFromInput(input)
      }
    }
  }

  showUpdateRequirementsDialog = signal<boolean>(false)

  updateRequirements(): void {
    this.showUpdateRequirementsDialog.set(true)
    // reset filters when accessing requirement update menu
    this.onDeselectAllFilters()
  }

  handleRequirementsUpdate(
    selectedRequirements: QwFileResourceLabel[],
    vm: ViewModel,
  ): void {
    this.polDataService
      .updateOrderLineRequirementsFE(
        vm.polState.purchaseOrderLine.id,
        selectedRequirements,
      )
      .subscribe({
        next: () => {
          this.showUpdateRequirementsDialog.set(false)
          this.userInterfaceService.showPopup(DIALOG_MESSAGES.UPDATE_SUCCESS)
        },
        error: (error: Error) => {
          logger.error("Error updating requirements:", error)
          this.userInterfaceService.showPopup(DIALOG_MESSAGES.UPDATE_FAIL)
        },
      })
  }

  // Context menu for single file
  openContextMenuForSingleFile(
    file: ExtendedFileResource,
    vm: ViewModel,
    event: MouseEvent,
  ): void {
    // Prevent click from bubbling to document and triggering the outside click handler
    // event.preventDefault();
    event.stopPropagation()

    const buttonRect = (
      event.currentTarget as HTMLElement
    ).getBoundingClientRect()

    this.navigateToSelection(vm, file)

    setTimeout(() => {
      const items = this.getMenuItemsForSingleFile(file, vm)
      // Use the custom positioning overload that already exists
      this.contextMenuService.open(items, file, {
        type: "custom",
        rect: buttonRect,
      })
    }, 0)
  }

  // Selected revision is by default the first one
  // Once revisions can be uploaded, we need to set this based on the selected revision
  navigateToSelection(vm: ViewModel, file: ExtendedFileResource): void {
    this.router.navigate(
      [AppRoutes.fullPath("polFiles", vm.polState.purchaseOrderLine.id)],
      {
        queryParams: { fr: file.id },
      },
    )
  }

  shareOrUnshareFileResourceRevisions(
    file: QwFileResourceView,
    vm: ViewModel,
    share: boolean,
  ): void {
    // Check how many revisions File Resource has
    if (file.revisions.length > 1) {
      // Currently revisions are not fully supported
      return
    }

    // Get other tenant ID (the one we want to share with or unshare from)
    const orderInfo = vm.polState.purchaseOrder.info as QwPurchaseOrderInfoView
    const otherTenantId =
      vm.polState.tenantId === orderInfo.supplier.id
        ? vm.polState.purchaseOrder.customer.id
        : orderInfo.supplier.id

    // Use the first (and only) revision
    const revisionId = file.revisions[0].id

    this.fileResourcesDataService
      .toggleFileResourceRevisionAccessFE(revisionId, otherTenantId, share)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.userInterfaceService.showPopup(
              share ? DIALOG_MESSAGES.SHARED : DIALOG_MESSAGES.UNSHARED,
            )
          }
          logger.log("success")
        },
        error: (error) => {
          logger.error(`Error ${share ? "sharing" : "unsharing"} file:`, error)
          this.userInterfaceService.showPopup(
            `${DIALOG_MESSAGES.ERROR}: ${error}`,
          )
        },
      })
  }

  private getMenuItemsForSingleFile(
    file: ExtendedFileResource,
    vm: ViewModel,
  ): ContextMenuItem[] {
    const items: ContextMenuItem[] = []

    // Rename option
    items.push({
      label: $localize`Rename`,
      icon: faPenToSquare,
      action: () => {
        this.startRenaming(file.id)
      },
      type: "default",
      visible: () => true,
    })

    // Download available only for files other
    // TODO: Control this properly
    if (
      true
      // No great way of differentiating who can download what yet
      // Temporarily it is decided that whoever has access can download too
      // file.label === QwFileResourceLabel.Other ||
      // file.label === QwFileResourceLabel.DimensionalResults ||
      // file.label === QwFileResourceLabel.InitialSampleInspectionReport
    ) {
      items.push({
        label: $localize`Download`,
        icon: faDownload,
        // This is a poor work around. Using revisionIds everywhere will require a greater overhaul.
        action: () => {
          this.downloadFile(file.revisions[0].id, file.displayName)
        },
        type: "default",
        visible: () => true,
      })
    }

    if (
      vm.polState.purchaseOrder.info.type !== QwOrderType.Production &&
      vm.polState.tenantId === file.owner.id &&
      !this.isFileShared()(
        file,
        vm.polState,
        vm.fileResourceState.fileResourcesMetadata,
      )
    ) {
      items.push({
        label: $localize`Share`,
        icon: faUserGroup,
        action: () => {
          this.shareOrUnshareFileResourceRevisions(file, vm, true)
        },
        type: "default",
      })
    }

    if (
      vm.polState.tenantId === file.owner.id &&
      this.isFileShared()(
        file,
        vm.polState,
        vm.fileResourceState.fileResourcesMetadata,
      )
    ) {
      items.push({
        label: $localize`Unshare`,
        icon: faUser,
        action: () => {
          this.shareOrUnshareFileResourceRevisions(file, vm, false)
        },
        type: "default",
      })
    }

    // Link/Unlink to material options - only if user owns the file and material exists
    if (
      vm.polState.tenantId === file.owner.id &&
      vm.polState.material?.id &&
      file.label === QwFileResourceLabel.TechnicalDrawing
    ) {
      const isLinkedToMaterial = file.isInheritedFromMaterial === true
      const materialRef = vm.polState.material?.internalReference || "Material"

      if (!isLinkedToMaterial) {
        items.push({
          label: $localize`Attach to "` + materialRef + `"`,
          icon: faLink,
          action: () => {
            this.linkFileToMaterial(file, vm)
          },
        })
      } else {
        items.push({
          label: $localize`Detach from "` + materialRef + `"`,
          icon: faUnlink,
          action: () => {
            this.unlinkFileFromMaterial(file, vm)
          },
        })
      }
    }

    // Delete option - only if user owns the file
    if (vm.polState.tenantId === file.owner.id) {
      items.push({
        label: $localize`Delete`,
        icon: faTrash,
        action: () => {
          this.deleteFile(file.id)
        },
        type: "danger",
      })
    }

    return items
  }

  // Cross component ===========================================================
  protected uploadInProgress = signal<boolean>(false)
  uploadInProgressHandler(value: boolean) {
    this.uploadInProgress.set(value)
  }

  // UI/View ===================================================================
  localizedFilterButtonTitle = computed(
    () =>
      (isRequirementFilterApplied: boolean): string => {
        if (isRequirementFilterApplied) {
          return $localize`Clear Filter`
        }
        return $localize`No filters applied`
      },
  )

  localizedFilterResultMessage = computed(
    () =>
      (selectedRequirement: boolean): string => {
        if (selectedRequirement) {
          return $localize`No matching file...`
        }
        return $localize`No file to show...`
      },
  )
}
