import { Injectable } from "@angular/core"
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  Observable,
  of,
  shareReplay,
} from "rxjs"
import { map, switchMap, tap } from "rxjs/operators"

import {
  QwDeleteFileResourcesInput,
  QwDeleteFileResourcesOutput,
  QwFileResourceLabel,
  QwFileResourceMetadataView,
  QwFileResourceRenameInput,
  QwFileResourceRenameOutput,
  QwFileResourceView,
  QwLinkFileToMaterialInput,
  QwLinkFileToMaterialOutput,
  QwListFileResourcesInput,
  QwMultipartSchemaApiV1FileResourceRevision,
  QwNewFileResourceRevisionOutput,
  QwToggleFileResourceRevisionAccessInput,
  QwToggleFileResourceRevisionAccessOutput,
  QwUnlinkFileFromMaterialInput,
  QwUnlinkFileFromMaterialOutput,
} from "@qw-api/models"
import { QwFileResourceService } from "@qw-api/services/qw-file-resource.service"

interface FileResourcesWithMetadata {
  fileResources: QwFileResourceView[]
  fileResourcesMetadata: QwFileResourceMetadataView[]
}

interface FileResourcesWithMetadataAndSource {
  fileResources: QwFileResourceView[]
  fileResourcesMetadata: QwFileResourceMetadataView[]
  materialFileIds: Set<number> // IDs of files that come from material connection
}

@Injectable({
  providedIn: "root",
})
export class FileResourcesDataService {
  constructor(private fileResourceAPIWrapperService: QwFileResourceService) {}

  private refreshAllTrigger = new BehaviorSubject<null>(null)

  get refreshAllTrigger$(): Observable<null> {
    return this.refreshAllTrigger.asObservable()
  }

  private fileResourcesData$ = new BehaviorSubject<{
    fileResources: QwFileResourceView[]
    fileResourcesMetadata: QwFileResourceMetadataView[]
  } | null>(null)

  private fetchFileResourcesAndMetadata(
    params: Partial<QwListFileResourcesInput> = {},
  ): Observable<FileResourcesWithMetadata> {
    const defaultParams: QwListFileResourcesInput = {
      count: 50,
      offset: 0,
      ...params,
    }

    return this.fileResourceAPIWrapperService
      .listFileResources({
        body: defaultParams,
      })
      .pipe(
        switchMap((output) => {
          const fileResourceIds = output.fileResources.map((fr) => fr.id)
          return this.fileResourceAPIWrapperService
            .listFileResourcesMetadata({
              body: { inFileResourceIds: fileResourceIds },
            })
            .pipe(
              map((outputFileMetadata) => ({
                fileResources: output.fileResources,
                fileResourcesMetadata: outputFileMetadata.metadataItems,
              })),
            )
        }),
      )
  }

  addFileResourceFE(
    body: QwMultipartSchemaApiV1FileResourceRevision,
  ): Observable<QwNewFileResourceRevisionOutput> {
    return this.fileResourceAPIWrapperService
      .addFileResourceRevision({ body })
      .pipe(
        tap((response) => {
          if (response.info) {
            this.refreshAllTrigger.next(null)
          }
        }),
      )
  }

  renameFileResourceFE(
    fileResourceId: number,
    newDisplayName: string,
  ): Observable<QwFileResourceRenameOutput> {
    const renameInput: QwFileResourceRenameInput = { newDisplayName }
    return this.fileResourceAPIWrapperService
      .renameFileResource({
        fileResourceId,
        body: renameInput,
      })
      .pipe(
        tap(() => {
          this.refreshAllTrigger.next(null)
        }),
      )
  }

  deleteFileResourceFE(
    fileResourceId: number,
  ): Observable<QwDeleteFileResourcesOutput> {
    const deleteInput: QwDeleteFileResourcesInput = {
      fileResourceIds: [fileResourceId],
    }
    return this.fileResourceAPIWrapperService
      .deleteFileResources({ body: deleteInput })
      .pipe(
        tap(() => {
          logger.log(`Deleted fr: ${fileResourceId}`)
          this.refreshAllTrigger.next(null)
        }),
      )
  }

  downloadFileResourceFE(fileResourceRevisionId: number): Observable<Blob> {
    return this.fileResourceAPIWrapperService
      .getFileResourceDownload({
        fileResourceRevisionId: fileResourceRevisionId,
      })
      .pipe(
        map((response) => {
          if (response instanceof Blob) {
            return response
          }
          throw new Error("Response is not a Blob")
        }),
        catchError((error) => {
          logger.error("Error downloading file:", error)
          throw error
        }),
      )
  }

  toggleFileResourceRevisionAccessFE(
    fileResourceRevisionId: number,
    tenantId: number,
    enabled: boolean,
  ): Observable<QwToggleFileResourceRevisionAccessOutput> {
    const toggleInput: QwToggleFileResourceRevisionAccessInput = {
      fileResourceRevisionId,
      tenantId,
      enabled,
    }

    return this.fileResourceAPIWrapperService
      .toggleFileResourceRevisionAccess({ body: toggleInput })
      .pipe(
        tap(() => {
          this.refreshAllTrigger.next(null)
        }),
        catchError((error) => {
          logger.error("Error toggling file resource access:", error)
          throw error
        }),
      )
  }

  getFileResourcesByOrderLineAndMaterialId(
    orderLineId: number,
    materialId: number | null,
  ): Observable<FileResourcesWithMetadataAndSource | null> {
    const orderLineCall = this.fetchFileResourcesAndMetadata({
      linkedToOrderLineIds: [orderLineId],
    })

    if (!materialId) {
      // No material ID, just return order line files
      return orderLineCall.pipe(
        map((data) => ({
          ...data,
          materialFileIds: new Set<number>(), // No material files
        })),
        tap((data) => {
          this.fileResourcesData$.next(data)
        }),
        catchError((err) => {
          logger.error(err)
          return of(null)
        }),
        shareReplay(1),
      )
    }

    // Combine files from both order line and material
    return combineLatest([
      orderLineCall,
      this.fetchFileResourcesAndMetadata({
        linkedToMaterialIds: [materialId],
      }),
    ]).pipe(
      map(([orderLineData, materialData]) => {
        // Track which files come from material
        const materialFileIds = new Set(
          materialData.fileResources.map((f) => f.id),
        )

        // Combine files and metadata from both sources
        const allFiles = [
          ...orderLineData.fileResources,
          ...materialData.fileResources,
        ]
        const allMetadata = [
          ...orderLineData.fileResourcesMetadata,
          ...materialData.fileResourcesMetadata,
        ]

        // Deduplicate files by ID (in case a file is linked to both order line and material)
        const seenFileIds = new Set<number>()
        const deduplicatedFiles = allFiles.filter((file) => {
          if (seenFileIds.has(file.id)) {
            return false
          }
          seenFileIds.add(file.id)
          return true
        })

        // Deduplicate metadata by fileResourceId
        const seenMetadataIds = new Set<number>()
        const deduplicatedMetadata = allMetadata.filter((metadata) => {
          if (seenMetadataIds.has(metadata.fileResourceId)) {
            return false
          }
          seenMetadataIds.add(metadata.fileResourceId)
          return true
        })

        return {
          fileResources: deduplicatedFiles,
          fileResourcesMetadata: deduplicatedMetadata,
          materialFileIds,
        }
      }),
      tap((data) => {
        this.fileResourcesData$.next(data)
      }),
      catchError((err) => {
        logger.error(err)
        return of(null)
      }),
      shareReplay(1),
    )
  }

  getTechnicalDrawingsByOrderLineAndMaterialId(
    orderLineId: number,
    materialId: number,
  ): Observable<QwFileResourceView[] | null> {
    return combineLatest([
      // Call 1: Files linked to order line
      this.fetchFileResourcesAndMetadata({
        linkedToOrderLineIds: [orderLineId],
        inFileResourceLabels: [QwFileResourceLabel.TechnicalDrawing],
      }),
      // Call 2: Files linked to material
      this.fetchFileResourcesAndMetadata({
        linkedToMaterialIds: [materialId],
        inFileResourceLabels: [QwFileResourceLabel.TechnicalDrawing],
      }),
    ]).pipe(
      map(([orderLineData, materialData]) => {
        // Merge and deduplicate by revision ID
        const allFiles = [
          ...orderLineData.fileResources,
          ...materialData.fileResources,
        ]
        const allMetadata = [
          ...orderLineData.fileResourcesMetadata,
          ...materialData.fileResourcesMetadata,
        ]
        const seenRevisionIds = new Set<number>()

        const deduplicated = allFiles.filter((fileResource) => {
          // Check if ANY of this file's revisions have been seen
          const hasUnseenRevision = fileResource.revisions.some((revision) => {
            if (seenRevisionIds.has(revision.id)) {
              return false // This revision was already seen
            }
            seenRevisionIds.add(revision.id)
            return true // This revision is new
          })

          return hasUnseenRevision
        })

        // Get file resource IDs that were kept after deduplication
        const keptFileResourceIds = new Set(deduplicated.map((fr) => fr.id))

        // Filter metadata to only include metadata for kept file resources
        const deduplicatedMetadata = allMetadata.filter((metadata) =>
          keptFileResourceIds.has(metadata.fileResourceId),
        )

        return {
          fileResources: deduplicated,
          fileResourcesMetadata: deduplicatedMetadata,
        }
      }),
      tap((data) => {
        // Update the data stream with merged results
        this.fileResourcesData$.next(data)
      }),
      map((data) => data.fileResources),
      catchError((err) => {
        logger.error(err)
        return of(null)
      }),
      shareReplay(1),
    )
  }

  linkFileToMaterialFE(
    fileResourceRevisionId: number,
    materialId: number,
  ): Observable<QwLinkFileToMaterialOutput> {
    const linkInput: QwLinkFileToMaterialInput = {
      fileResourceRevisionId,
      materialId,
    }

    return this.fileResourceAPIWrapperService
      .linkFileToMaterial({ body: linkInput })
      .pipe(
        tap(() => {
          this.refreshAllTrigger.next(null)
        }),
        catchError((error) => {
          logger.error("Error linking file to material:", error)
          throw error
        }),
      )
  }

  unlinkFileFromMaterialFE(
    fileResourceRevisionId: number,
    materialId: number,
  ): Observable<QwUnlinkFileFromMaterialOutput> {
    const unlinkInput: QwUnlinkFileFromMaterialInput = {
      fileResourceRevisionId,
      materialId,
    }

    return this.fileResourceAPIWrapperService
      .unlinkFileFromMaterial({ body: unlinkInput })
      .pipe(
        tap(() => {
          this.refreshAllTrigger.next(null)
        }),
        catchError((error) => {
          logger.error("Error unlinking file from material:", error)
          throw error
        }),
      )
  }
}
