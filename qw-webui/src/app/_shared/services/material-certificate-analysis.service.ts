import { Injectable } from "@angular/core"
import {
  catchError,
  distinctUntilChanged,
  Observable,
  of,
  switchMap,
  takeWhile,
  timer,
} from "rxjs"

import { QwMaterialCertificateAnalysisResponseWithStatus } from "@qw-api/models"
import { QwFileResourceService } from "@qw-api/services/qw-file-resource.service"

@Injectable()
export class MaterialCertificateAnalysisService {
  constructor(private fileResourceWrapperAPIService: QwFileResourceService) {}

  getMaterialCertificateAnalysis(
    fileResourceRevisionId: number,
  ): Observable<QwMaterialCertificateAnalysisResponseWithStatus> {
    return this.fileResourceWrapperAPIService
      .getMaterialCertificateAnalysis({ fileResourceRevisionId })
      .pipe(
        switchMap((response) =>
          response.status === "ANALYZING"
            ? this.pollForAnalysisCompletion(fileResourceRevisionId)
            : of(response),
        ),
        catchError((error: any) =>
          error.status === 404
            ? of({ status: null, data: null, message: "No analysis found" })
            : of({
                status: "FAILURE",
                data: null,
                message: "Error loading analysis",
              }),
        ),
      )
  }

  private pollForAnalysisCompletion(
    fileResourceRevisionId: number,
  ): Observable<QwMaterialCertificateAnalysisResponseWithStatus> {
    return timer(0, 10000).pipe(
      switchMap(() =>
        this.fileResourceWrapperAPIService.getMaterialCertificateAnalysis({
          fileResourceRevisionId,
        }),
      ),
      takeWhile(
        (response, index) => response.status === "ANALYZING" && index < 10,
        true,
      ),
      distinctUntilChanged((a, b) => a.status === b.status),
    )
  }
}
