import { Injectable } from "@angular/core"
import { NormalizedQuadPts } from "@qw-shared-services/drawing-resource-state.service"
import {
  catchError,
  distinctUntilChanged,
  Observable,
  of,
  switchMap,
  takeWhile,
  tap,
  timer,
} from "rxjs"

import {
  QwTechnicalDrawingAnalysisResponseWithStatus,
  QwToleranceLookupInput,
  QwToleranceLookupOutput,
  QwToleranceLookupResult,
  QwToleranceStandardView,
  QwTolerancesView,
} from "@qw-api/models"
import { QwFileResourceService, QwTolerancesService } from "@qw-api/services"

@Injectable({
  providedIn: "root",
})
export class DrawingAnalysisService {
  constructor(
    private fileResourceWrapperAPIService: QwFileResourceService,
    private tolerancesWrapperAPIService: QwTolerancesService,
  ) {}

  // Tolerances ================================================================
  getTolerancesFE(): Observable<QwTolerancesView> {
    return this.tolerancesWrapperAPIService.getTolerances().pipe(
      tap((result) => {
        logger.log("Fetched tolerances:", result)
      }),
      catchError((error) => {
        logger.error("Error fetching tolerances:", error)
        return of({
          toleranceStandards: [] as QwToleranceStandardView[],
        })
      }),
    )
  }

  toleranceLookupFE(
    input: QwToleranceLookupInput,
  ): Observable<QwToleranceLookupOutput> {
    return this.tolerancesWrapperAPIService
      .toleranceLookup({ body: input })
      .pipe(
        tap((result) => {
          logger.log("Tolerance lookup result:", result)
        }),
        catchError((error) => {
          logger.error("Error looking up tolerance:", error)
          return of({
            tolerance: null as QwToleranceLookupResult | null,
          })
        }),
      )
  }

  // Analysis ==================================================================

  getDrawingRevisionAnalysisFE(
    fileResourceRevisionId: number,
    pageIndex?: number | null,
  ): Observable<QwTechnicalDrawingAnalysisResponseWithStatus> {
    return this.fileResourceWrapperAPIService
      .getDrawingRevisionAnalysis({
        fileResourceRevisionId,
        pageIndex,
      })
      .pipe(
        switchMap((response) =>
          response.status === "ANALYZING"
            ? this.pollForAnalysisCompletion(fileResourceRevisionId, pageIndex)
            : of(response),
        ),
        catchError((error: any) =>
          error.status === 404
            ? of({ status: null, data: null, message: "No analysis found" })
            : of({
                status: "FAILURE",
                data: null,
                message: "Error loading analysis",
              }),
        ),
      )
  }

  private pollForAnalysisCompletion(
    fileResourceRevisionId: number,
    pageIndex?: number | null,
  ): Observable<QwTechnicalDrawingAnalysisResponseWithStatus> {
    return timer(0, 60000).pipe(
      switchMap(() =>
        this.fileResourceWrapperAPIService.getDrawingRevisionAnalysis({
          fileResourceRevisionId,
          pageIndex,
        }),
      ),
      takeWhile(
        (response, index) => response.status === "ANALYZING" && index < 10,
        true,
      ),
      distinctUntilChanged((a, b) => a.status === b.status),
    )
  }

  // Find if quads are in the plan or not by box comparison ====================
  // Backend doesn't store the quad ids at the moment, so if they are selected
  // by user and needs to be removed the original quads, we identify them by
  // this way
  areQuadsEqual(quad1: NormalizedQuadPts, quad2: NormalizedQuadPts): boolean {
    const tolerance = 0.001 // Adjust later if needed
    return (
      [
        "x1",
        "y1",
        "x2",
        "y2",
        "x3",
        "y3",
        "x4",
        "y4",
      ] as (keyof NormalizedQuadPts)[]
    ).every((key) => Math.abs(quad1[key] - quad2[key]) < tolerance)
  }
}
