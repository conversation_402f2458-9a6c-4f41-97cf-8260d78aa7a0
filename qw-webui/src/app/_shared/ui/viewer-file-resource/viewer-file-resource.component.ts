import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, NgI<PERSON> } from "@angular/common"
import { Component, input } from "@angular/core"
import { toObservable } from "@angular/core/rxjs-interop"
import { API_ROUTES } from "@qw-core/constants/app-routes"
import { CollaboraService } from "@qw-shared-services/collabora.service"
import { map, shareReplay, switchMap } from "rxjs"

import { ViewerCollaboraComponent } from "./viewer-collabora/viewer-collabora.component"
import { ViewerDrawingComponent } from "./viewer-drawing/viewer-drawing.component"
import { ViewerImageComponent } from "./viewer-image/viewer-image.component"
import { ViewerMaterialCertificateComponent } from "./viewer-material-certificate/viewer-material-certificate.component"

import {
  QwFileResourceLabel,
  QwFileResourceRevisionView,
  QwFileResourceView,
} from "@qw-api/models"

@Component({
  selector: "qw-viewer-file-resource",
  templateUrl: "./viewer-file-resource.component.html",
  standalone: true,
  imports: [
    NgIf,
    ViewerCollaboraComponent,
    ViewerDrawingComponent,
    ViewerMaterialCertificateComponent,
    ViewerImageComponent,
    AsyncPipe,
    JsonPipe,
  ],
})
export class ViewerFileResourceComponent {
  fileResource = input.required<QwFileResourceView>()

  constructor(private collaboraService: CollaboraService) {}

  private fileResource$ = toObservable(this.fileResource)

  protected readonly vm$ = this.fileResource$.pipe(
    // Wait for Collabora discovery to be ready first
    // Otherwise causing issues at first load
    switchMap((resource) =>
      this.collaboraService.discoveryInit$.pipe(map(() => resource)),
    ),
    map((resource) => ({
      fileType: this.getFileType(resource),
      resource,
    })),
    shareReplay(1),
  )

  private getFileType(
    resource: QwFileResourceView,
  ):
    | "collabora"
    | "image"
    | "video"
    | "technicalDrawing"
    | "materialCertificate"
    | "other" {
    if (resource.label === QwFileResourceLabel.TechnicalDrawing) {
      return "technicalDrawing"
    }
    if (resource.label === QwFileResourceLabel.MaterialCertificate) {
      return "materialCertificate"
    }
    const mimeType = resource.mimeType
    if (mimeType.startsWith("image/")) {
      return "image"
    }
    if (mimeType.startsWith("video/")) {
      return "video"
    }
    const isCollabora = this.collaboraService.supportsFile(resource.displayName)
    if (isCollabora) {
      return "collabora"
    }
    return "other"
  }

  protected getFileRevisionUrl(revision: QwFileResourceRevisionView): string {
    return API_ROUTES.fileRevision.url(revision.id)
  }
}
