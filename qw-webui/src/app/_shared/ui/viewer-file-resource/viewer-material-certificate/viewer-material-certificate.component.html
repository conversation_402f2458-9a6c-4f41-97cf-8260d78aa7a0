<ng-container *ngIf="vm() as vm">
  <div class="flex h-full w-full flex-col">
    <!-- Page navigation control -->
    <div
      class="mb-1 flex h-8 shrink-0 items-center rounded bg-gray-200 px-2 py-1"
    >
      <div class="flex items-center space-x-2">
        <!-- Page Navigation -->
        <div
          *ngIf="vm.viewMode === 'default'"
          class="flex items-center space-x-1 rounded-md bg-white"
        >
          <button
            class="flex h-7 w-7 items-center justify-center rounded transition-colors"
            [ngClass]="
              vm.canGoPrev ? 'text-gray-700 hover:bg-gray-100' : 'text-gray-300'
            "
            [disabled]="!vm.canGoPrev"
            (click)="changePageIndex('prev')"
          >
            <fa-icon [icon]="faChevronLeft" class="text-xs"></fa-icon>
          </button>

          <span class="select-none px-2 text-xs font-medium text-gray-700">
            <ng-container i18n>Page</ng-container>
            <ng-container>
              {{ vm.currentPageIndex + 1 }}/{{ vm.totalPages }}
            </ng-container>
          </span>

          <button
            class="flex h-7 w-7 items-center justify-center rounded transition-colors"
            [ngClass]="
              vm.canGoNext ? 'text-gray-700 hover:bg-gray-100' : 'text-gray-300'
            "
            [disabled]="!vm.canGoNext"
            (click)="changePageIndex('next')"
          >
            <fa-icon [icon]="faChevronRight" class="text-xs"></fa-icon>
          </button>
        </div>

        <!-- Layout Fit Toggle -->
        <div
          *ngIf="vm.viewMode === 'default'"
          class="inline-flex h-7 w-7 cursor-pointer items-center justify-center rounded-md bg-gray-400 text-white hover:bg-gray-700"
          [ngClass]="{ 'rotate-90': vm.isFitToHeight }"
          (click)="toggleLayout()"
        >
          <fa-icon
            [icon]="faRulerHorizontal"
            [title]="vm.isFitToHeight ? 'Fit to width' : 'Fit to height'"
            class="text-sm"
          ></fa-icon>
        </div>
        <!-- Analysis View Toggle -->
        <div
          class="relative inline-flex h-7 w-7 cursor-pointer items-center justify-center rounded-md text-white transition-colors hover:bg-qw-primary-500"
          [ngClass]="
            vm.viewMode === 'analysis' ? 'bg-qw-primary-500' : 'bg-gray-400'
          "
          (click)="toggleViewMode()"
        >
          <fa-icon
            [icon]="faChartBar"
            [title]="
              vm.viewMode === 'analysis' ? 'Close Analysis' : 'Show Analysis'
            "
            class="text-sm"
          ></fa-icon>
          <!-- Analysis Status Dot -->
          <div class="absolute -right-1.5 -top-1">
            <qw-analysis-status-indicator
              [status]="vm.analysisStatus"
              [message]="vm.analysisMessage"
            ></qw-analysis-status-indicator>
          </div>
        </div>
        <!-- Results Filter Toggle -->
        <div
          *ngIf="vm.viewMode === 'analysis'"
          class="inline-flex h-7 w-7 cursor-pointer items-center justify-center rounded-md text-white transition-colors hover:bg-qw-primary-500"
          [ngClass]="vm.showFilters ? 'bg-qw-primary-500' : 'bg-gray-400'"
          (click)="toggleFilters()"
        >
          <fa-icon
            [icon]="faFilter"
            [title]="filtersToggleTitle()(vm.showFilters)"
            class="text-sm"
          ></fa-icon>
        </div>
        <!-- Plate Navigation -->
        <div
          *ngIf="vm.viewMode === 'analysis'"
          class="flex items-center space-x-1"
        >
          <button
            *ngFor="let plate of vm.plateResults.currentPlates; let i = index"
            (click)="setCurrentPlateIndex(i)"
            class="flex h-5 w-5 items-center justify-center rounded-full text-xs text-white transition-colors"
            [class.bg-qw-warn-200]="!plate.isOk && i !== vm.currentPlateIndex"
            [class.bg-qw-warn-500]="!plate.isOk && i === vm.currentPlateIndex"
            [class.bg-qw-primary-200]="plate.isOk && i !== vm.currentPlateIndex"
            [class.bg-qw-primary-500]="plate.isOk && i === vm.currentPlateIndex"
          >
            {{ i + 1 }}
          </button>
        </div>
      </div>
    </div>
    <!-- Certificate display area -->
    <div
      *ngIf="!!vm.currentPageUrl"
      class="mt-1 flex h-full w-full flex-col overflow-hidden"
    >
      <qw-material-certificate-analysis-results-filter
        class="w-full shrink-0"
        *ngIf="vm.showFilters && vm.viewMode === 'analysis'"
      />
      <qw-material-certificate-analysis-results
        *ngIf="vm.viewMode === 'analysis'"
        class="flex-1 overflow-y-auto"
        [analysis]="vm.analysis"
      />
      <div
        *ngIf="vm.viewMode === 'default'"
        class="h-full w-full"
        [ngClass]="{
          'overflow-x-auto': vm.isFitToHeight,
          'overflow-y-auto': !vm.isFitToHeight,
        }"
      >
        <div
          [ngClass]="{
            'h-full': vm.isFitToHeight,
            'w-full': !vm.isFitToHeight,
          }"
        >
          <qw-viewer-image
            [imgUrl]="vm.currentPageUrl"
            [isFitToHeight]="vm.isFitToHeight"
          />
        </div>
      </div>
    </div>
  </div>
</ng-container>
