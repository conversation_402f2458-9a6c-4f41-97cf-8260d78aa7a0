import { Async<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from "@angular/common"
import {
  Component,
  computed,
  effect,
  Injector,
  input,
  signal,
} from "@angular/core"
import { takeUntilDestroyed, toObservable } from "@angular/core/rxjs-interop"
import { ActivatedRoute } from "@angular/router"
import { FaIconComponent } from "@fortawesome/angular-fontawesome"
import {
  faChevronLeft,
  faChevronRight,
  faCircle,
  faCog,
  faDiagramProject,
  faFont,
  faNoteSticky,
  faRuler,
  faRulerHorizontal,
  faSquarePlus,
  faWandMagicSparkles,
  faWrench,
} from "@fortawesome/free-solid-svg-icons"
import { API_ROUTES } from "@qw-core/constants/app-routes"
import { ScreenSizeService } from "@qw-core/services/screen-size.service"
import { AgentActionDispatcherService } from "@qw-shared-services/agent-action-dispatcher.service"
import { AgentService } from "@qw-shared-services/agent.service"
import { ChatDataService } from "@qw-shared-services/chat-data.service"
import { DrawingAnalysisService } from "@qw-shared-services/drawing-analysis.service"
import { DrawingQuadProcessingService } from "@qw-shared-services/drawing-quad-processing.service"
import {
  DrawingResourceState,
  DrawingViewerStateService,
  Pmi,
} from "@qw-shared-services/drawing-resource-state.service"
import { PolStateService } from "@qw-shared-services/pol-state.service"
import { WindowSelectionService } from "@qw-shared-services/window-selection.service"
import { AnalysisStatusIndicatorComponent } from "@qw-shared-ui/analysis-status-indicator/analysis-status-indicator.component"
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  EMPTY,
  map,
  Observable,
  of,
  shareReplay,
  switchMap,
  take,
  tap,
} from "rxjs"

import { DrawingDiscussionStarterComponent } from "./drawing-page/drawing-discussion-starter/drawing-discussion-starter.component"
import { DrawingPageComponent } from "./drawing-page/drawing-page.component"

import {
  QwFileResourceView,
  QwImportance,
  QwInspectionDrawingQuad,
  QwNormalizedPmiBlockAnalysisResult,
  QwUnit,
} from "@qw-api/models"

interface DrawingViewerOptions {
  showStartDrawingDiscussionButton: boolean
  hideVersionsIfOnlyOne: boolean
}

// Define the analysis state type
interface AnalysisData {
  status: string
  message?: string
  results: Record<string, QwNormalizedPmiBlockAnalysisResult[]>
}

type PageDirection = "next" | "prev"

@Component({
  selector: "qw-viewer-drawing",
  templateUrl: "./viewer-drawing.component.html",
  standalone: true,
  imports: [
    NgIf,
    FaIconComponent,
    NgClass,
    DrawingPageComponent,
    DrawingDiscussionStarterComponent,
    AnalysisStatusIndicatorComponent,
    AsyncPipe,
  ],
})
export class ViewerDrawingComponent {
  // Initialization
  constructor(
    private activatedRoute: ActivatedRoute,
    private actionDispatcher: AgentActionDispatcherService,
    private agentService: AgentService,
    private chatDataService: ChatDataService,
    private drawingQuadProcessingService: DrawingQuadProcessingService,
    private drawingAnalysisService: DrawingAnalysisService,
    private drawingViewerStateService: DrawingViewerStateService,
    private polStateService: PolStateService,
    private screenSizeService: ScreenSizeService,
    private windowSelectionService: WindowSelectionService,
    private injector: Injector,
  ) {
    // Reset page index and UI state when revision changes
    effect(
      () => {
        const currentRevision = this.selectedDrawingRevision()
        if (!currentRevision) return

        this.selectedPageIndex.set(0)
        this.isShowingDiscussionQuads$.next(false)
      },
      { allowSignalWrites: true },
    )

    // Register actions with the AgentActionDispatcherService
    this.registerAgentActions()

    // Use combineLatest to properly handle both vm$ and agent visibility
    combineLatest([this.vm$, this.agentService.isVisible$])
      .pipe(takeUntilDestroyed())
      .subscribe(([viewModel, isVisible]) => {
        if (isVisible) {
          // Only pass relevant context to the agent service
          const relevantContext = {
            pmis: viewModel.pmis || [],
            currentRevisionId: viewModel.currentRevisionId,
            currentPageIndex: viewModel.currentPageIndex,
            inspectionPlanState:
              viewModel.drawingResourceState?.inspectionPlanState || {},
          }
          this.agentService.setContext({ viewerDrawing: relevantContext })
        }
      })
  }

  // Static References

  // Bindings
  protected readonly faNoteSticky = faNoteSticky
  protected readonly faSquarePlus = faSquarePlus
  protected readonly faRulerHorizontal = faRulerHorizontal
  protected readonly faChevronLeft = faChevronLeft
  protected readonly faChevronRight = faChevronRight
  protected readonly faWandMagicSparkles = faWandMagicSparkles

  // PMI type filter icons
  protected readonly faCircle = faCircle // Diameter and legend
  protected readonly faRuler = faRuler // Linear
  protected readonly faFont = faFont // Angle
  protected readonly faCog = faCog // GDT
  protected readonly faWrench = faWrench // Thread
  protected readonly faDiagramProject = faDiagramProject // Roughness

  // Data Streams and State ====================================================

  // Inputs
  selectedTechnicalDrawingResource = input.required<QwFileResourceView>()
  options = input.required<DrawingViewerOptions>()

  // Signals
  private selectedPageIndex = signal<number>(0)
  // TODO: Currently user only interacts with one revision per file
  // That's why we are not updating selected revision in any place
  // Consider implementing this feature later for drawings
  private selectedRevision = signal<number>(0)
  private selectedDrawingRevision = computed(() => {
    return this.selectedTechnicalDrawingResource().revisions[
      this.selectedRevision()
    ]
  })

  private currentPageState = computed(() => {
    const revision = this.selectedDrawingRevision()
    // page_count is not available for previously uploaded drawings
    // In that case frontend will assume there is at least one page
    const totalPages = revision.pageCount || 1

    return {
      currentRevisionId: revision.id,
      currentRevision: revision,
      currentPageIndex: this.selectedPageIndex(),
      totalPages: totalPages,
      currentPageUrl: API_ROUTES.drawingFileRevisionPageImage.url(
        revision.id,
        this.selectedPageIndex(),
      ),
      canGoNext: this.selectedPageIndex() < totalPages - 1,
      canGoPrev: this.selectedPageIndex() > 0,
    }
  })

  private readonly currentPageState$ = toObservable(this.currentPageState, {
    injector: this.injector,
  })

  private readonly selectedDrawingRevision$ = toObservable(
    this.selectedDrawingRevision,
    {
      injector: this.injector,
    },
  )

  private readonly analysisData$: Observable<AnalysisData> = combineLatest({
    revision: this.selectedDrawingRevision$,
    currentPageState: this.currentPageState$,
  }).pipe(
    switchMap(({ revision, currentPageState }) => {
      if (!revision) {
        return EMPTY
      }

      return this.drawingAnalysisService
        .getDrawingRevisionAnalysisFE(
          revision.id,
          currentPageState.currentPageIndex,
        )
        .pipe(
          // tap((response) => {
          //   logger.log("Drawing revision analysis response:", response)
          // }),
          map(
            (response) =>
              ({
                status: response.status || "FAILURE",
                message: response.message,
                results: response.data?.results || {},
              }) as AnalysisData,
          ),
          catchError((error) => {
            logger.error("Error loading analysis data:", error)
            return of({
              status: "FAILURE",
              message: "Failed to load analysis data",
              results: {},
            } as AnalysisData)
          }),
        )
    }),
    shareReplay(1),
  )

  private polId$ = this.polStateService.getPolState().pipe(
    map((polState) => polState.purchaseOrderLine.id),
    shareReplay(1),
  )

  private readonly chatDiscussions$ = combineLatest({
    polId: this.polId$,
    params: this.activatedRoute.queryParams,
  }).pipe(
    switchMap(({ polId, params }) => {
      const fileResourceId = Number(params["fr"])
      if (!fileResourceId) return of([])

      return this.chatDataService
        .listDiscussionsByPolId(polId)
        .pipe(
          map((discussions) =>
            discussions.filter((d) => d.fileResourceId === fileResourceId),
          ),
        )
    }),
    shareReplay(1),
  )

  private isShowingDiscussionQuads$ = new BehaviorSubject<boolean>(false)

  private readonly discussionQuads$ = combineLatest({
    pageState: this.currentPageState$,
    discussions: this.chatDiscussions$,
    showQuads: this.isShowingDiscussionQuads$,
  }).pipe(
    map(({ pageState, discussions, showQuads }) =>
      showQuads
        ? discussions
            .filter((d) => d.quad.pageIndex === pageState.currentPageIndex)
            .map((d) => ({
              resolved: d.resolved,
              displayName: `${d.discussionId}P${d.quad.pageIndex}`,
              chatId: d.chatId,
              discussionId: d.discussionId,
              pts: d.quad,
            }))
        : [],
    ),
    shareReplay(1),
  )

  // This observable combines PMIs from analysis results and custom quads from the inspection plan
  // We mark custom quads with:
  // 1. negative pmiId values
  // 2. belongsToPlan = true
  // 3. importance = QwImportance.Special
  private readonly pmis$ = combineLatest({
    pageState: this.currentPageState$,
    analysisData: this.analysisData$,
    drawingState: this.drawingViewerStateService.getState(),
  }).pipe(
    map(({ pageState, analysisData, drawingState }) => {
      if (analysisData.status !== "COMPLETED" || !analysisData.results) {
        return []
      }

      const pageResults = Object.values(analysisData.results)[0] || []

      // Get all quads that are added to plan state if any
      const inPlanQuads =
        drawingState.inspectionPlanState.inspectionPlanConfig?.steps
          ?.filter(
            (step) =>
              step.drawingQuad?.pageIndex === pageState.currentPageIndex &&
              step.drawingQuad.drawingRevisionId ===
                pageState.currentRevisionId,
          )
          .map((step) => step.drawingQuad)
          .filter((quad): quad is QwInspectionDrawingQuad => quad !== null) ??
        []

      // Create PMIs from analysis results
      const analysisPmis = (
        pageResults as QwNormalizedPmiBlockAnalysisResult[]
      ).map((block, index): Pmi => {
        // Destructure to get everything except polygon
        const { polygon, ...rest } = block

        // Create the PMI object with points from the polygon
        const pmi: Pmi = {
          // Include all properties except polygon
          ...rest,

          pmiId: index,
          inRevisionId: pageState.currentRevisionId,
          inPageIndex: pageState.currentPageIndex,
          belongsToPlan: false,
          pts: {
            x1: polygon.points[0].x,
            y1: polygon.points[0].y,
            x2: polygon.points[1].x,
            y2: polygon.points[1].y,
            x3: polygon.points[2].x,
            y3: polygon.points[2].y,
            x4: polygon.points[3].x,
            y4: polygon.points[3].y,
          },
        }

        // Check if this PMI is part of the plan
        pmi.belongsToPlan = inPlanQuads.some((inPlanQuad) =>
          this.drawingAnalysisService.areQuadsEqual(inPlanQuad, pmi.pts),
        )

        return pmi
      })

      // Find custom quads that don't match any analysis PMIs
      // Custom quads can be of different action types (measurement, image, video, question-answer)
      // We provide a minimal result object with importance=Special to indicate it's a custom quad
      const customPmis = inPlanQuads
        .filter(
          (inPlanQuad) =>
            !analysisPmis.some((pmi) =>
              this.drawingAnalysisService.areQuadsEqual(inPlanQuad, pmi.pts),
            ),
        )
        .map(
          (customQuad, index): Pmi => ({
            // Custom PMIs have negative IDs to distinguish them
            pmiId: -(index + 1),
            inRevisionId: pageState.currentRevisionId,
            inPageIndex: pageState.currentPageIndex,
            belongsToPlan: true,
            // Use the quad points directly
            pts: {
              x1: customQuad.x1,
              y1: customQuad.y1,
              x2: customQuad.x2,
              y2: customQuad.y2,
              x3: customQuad.x3,
              y3: customQuad.y3,
              x4: customQuad.x4,
              y4: customQuad.y4,
            },
            // For custom quads, we need to provide a minimal result object
            // We use a special importance value (Special) to indicate this is a custom quad
            result: {
              importance: QwImportance.Special,
              count: 1,
              detail: {
                type: "LINEAR",
                nominal_value: 0,
                unit: QwUnit.Millimeter,
                lower_tol: null,
                upper_tol: null,
                raw_callout: null,
              },
            },
            // Add tolerance_source as null
            tolerance_source: null,
          }),
        )

      // Combine both types of PMIs
      const allPmis = [...analysisPmis, ...customPmis]

      // If no filters are active, return all PMIs
      return allPmis
    }),
    shareReplay(1),
  )

  private isFitToHeight$ = new BehaviorSubject<boolean>(false)
  private showLegend$ = new BehaviorSubject<boolean>(true)

  protected readonly vm$ = combineLatest({
    pageState: this.currentPageState$,
    pmis: this.pmis$,
    discussions: this.chatDiscussions$,
    discussionQuads: this.discussionQuads$,
    drawingResourceState: this.drawingViewerStateService.getState(),
    isMobile: this.screenSizeService.isMobile$,
    isShowingDiscussionQuads: this.isShowingDiscussionQuads$,
    isFitToHeight: this.isFitToHeight$,
    showLegend: this.showLegend$,
    analysisState: this.analysisData$,
  }).pipe(
    map((state) => ({
      ...state.pageState,
      pmis: state.pmis,
      drawingPanelOptions: this.options(),
      discussions: state.discussions,
      discussionQuads: state.discussionQuads,
      hasDiscussions: state.discussions.length > 0,
      unresolvedDiscussionsCount: state.discussions.filter((d) => !d.resolved)
        .length,
      isShowingDiscussionQuads: state.isShowingDiscussionQuads,
      drawingResourceState: state.drawingResourceState,
      discussionMode: state.drawingResourceState.mode === "discussion",
      defaultMode: state.drawingResourceState.mode === "default",
      inspectionPlanBuilderMode:
        state.drawingResourceState.mode === "custom" ||
        state.drawingResourceState.mode === "dim",
      isMobile: state.isMobile,
      isFitToHeight: state.isFitToHeight,
      showLegend: state.showLegend,
      // Analysis state
      analysisStatus: state.analysisState.status,
      analysisMessage: state.analysisState.message,
    })),
    shareReplay(1),
  )

  // Actions ===================================================================
  protected changePageIndex(direction: PageDirection): void {
    this.selectedPageIndex.update((current) =>
      direction === "next" ? current + 1 : current - 1,
    )
  }

  protected toggleDiscussions(): void {
    this.isShowingDiscussionQuads$.next(!this.isShowingDiscussionQuads$.value)
  }

  protected toggleLayout(): void {
    this.isFitToHeight$.next(!this.isFitToHeight$.value)
  }

  protected toggleLegend(): void {
    this.showLegend$.next(!this.showLegend$.value)
  }

  openDiscussionStarterDialog() {
    this.windowSelectionService.resetSelectionState()
    this.drawingViewerStateService.updateStateForDiscussion()
  }

  // Quad add methods ==========================================================
  protected addPmis(pmis: Pmi[], drs: DrawingResourceState): void {
    logger.log("addPMI called")
    if (pmis && pmis.length > 0) {
      this.drawingQuadProcessingService.addPmisToPlan(pmis, drs)
    }
  }

  // Agent action registration =================================================
  private registerAgentActions(): void {
    // Register action to add PMIs to plan
    this.actionDispatcher.registerAction(
      "viewerDrawing",
      "addPmisToPlan",
      (parameters: any) => {
        logger.log("Agent action: addPmisToPlan", parameters)
        if (parameters && parameters.pmiIds && parameters.pmiIds.length > 0) {
          // Get the current drawing state
          this.drawingViewerStateService
            .getState()
            .pipe(take(1))
            .subscribe((drawingState: DrawingResourceState) => {
              // Get the current PMIs
              this.pmis$.pipe(take(1)).subscribe((pmis: Pmi[]) => {
                // Filter PMIs by the provided IDs
                const pmisToAdd = pmis.filter((pmi: Pmi) =>
                  parameters.pmiIds.includes(pmi.pmiId),
                )

                if (pmisToAdd.length > 0) {
                  logger.log("Adding PMIs to plan:", pmisToAdd)
                  this.addPmis(pmisToAdd, drawingState)
                } else {
                  logger.warn(
                    "No matching PMIs found for IDs:",
                    parameters.pmiIds,
                  )
                }
              })
            })
        }
      },
    )
  }

  // UI/Views ==================================================================
  readonly zoomInstructions = computed(() => (isMobile: boolean): string => {
    if (isMobile) {
      return $localize`Pinch to zoom`
    }
    return $localize`CTRL+Wheel to zoom`
  })
}
