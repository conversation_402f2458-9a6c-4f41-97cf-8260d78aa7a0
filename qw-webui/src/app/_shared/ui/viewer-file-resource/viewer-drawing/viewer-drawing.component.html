<ng-container *ngIf="vm$ | async as vm">
  <div class="flex h-full w-full flex-col">
    <!--Drawing display control -->
    <div class="flex h-8 shrink-0 items-center rounded-t-md bg-gray-200 px-2">
      <div class="flex select-none truncate text-xs text-gray-500">
        <!-- Analysis Status -->
        <qw-analysis-status-indicator
          [status]="$any(vm.analysisStatus)"
          [showText]="true"
        ></qw-analysis-status-indicator>
        <!-- &nbsp;{{ zoomInstructions()(vm.isMobile) }} -->
        <!-- {{ vm.currentRevisionDisplayName}} -->
      </div>
      <div class="ml-auto">
        <div class="flex items-center space-x-2">
          <div class="flex h-full items-center space-x-2">
            <!-- PMI Type Filters -->
            <div
              *ngIf="vm.drawingResourceState.mode === 'dim'"
              class="flex items-center space-x-1"
            >
              <!-- Add all characteristics -->
              <div
                class="relative inline-flex h-7 w-7 cursor-pointer items-center justify-center rounded-md bg-gray-400 text-white hover:bg-qw-primary-500"
                (click)="addPmis(vm.pmis, vm.drawingResourceState)"
              >
                <fa-icon
                  [icon]="faWandMagicSparkles"
                  [title]="'Add all characteristics'"
                  class="text-sm"
                ></fa-icon>
              </div>
            </div>
            <!-- Toggle Comments -->
            <div
              *ngIf="vm.hasDiscussions && !vm.inspectionPlanBuilderMode"
              class="relative inline-flex h-7 w-7 cursor-pointer items-center justify-center rounded-md text-white hover:bg-qw-primary-500"
              [ngClass]="
                vm.isShowingDiscussionQuads
                  ? 'bg-qw-primary-500'
                  : 'bg-gray-400'
              "
              (click)="toggleDiscussions()"
            >
              <fa-icon
                [icon]="faNoteSticky"
                [title]="'Show Discussions'"
                class="text-sm"
              ></fa-icon>
              <div
                *ngIf="vm.unresolvedDiscussionsCount > 0"
                class="absolute -right-1 -top-1 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-qw-warn-500 text-xxxs font-medium"
              >
                {{ vm.unresolvedDiscussionsCount }}
              </div>
            </div>
            <!-- Page navigation -->
            <ng-container *ngIf="vm.totalPages > 1">
              <div class="flex items-center space-x-1 rounded-md bg-white">
                <button
                  class="flex h-7 w-7 items-center justify-center rounded transition-colors"
                  [ngClass]="
                    vm.canGoPrev
                      ? 'text-gray-700 hover:bg-gray-100'
                      : 'text-gray-300'
                  "
                  [disabled]="!vm.canGoPrev"
                  (click)="changePageIndex('prev')"
                >
                  <fa-icon [icon]="faChevronLeft" class="text-xs"></fa-icon>
                </button>

                <span class="px-2 text-xs font-medium text-gray-700">
                  <ng-container i18n>Page</ng-container>
                  <ng-container>
                    {{ vm.currentPageIndex + 1 }}/{{ vm.totalPages }}
                  </ng-container>
                </span>

                <button
                  class="flex h-7 w-7 items-center justify-center rounded transition-colors"
                  [ngClass]="
                    vm.canGoNext
                      ? 'text-gray-700 hover:bg-gray-100'
                      : 'text-gray-300'
                  "
                  [disabled]="!vm.canGoNext"
                  (click)="changePageIndex('next')"
                >
                  <fa-icon [icon]="faChevronRight" class="text-xs"></fa-icon>
                </button>
              </div>
            </ng-container>
            <!-- Toggle Legend -->
            <div
              *ngIf="
                vm.inspectionPlanBuilderMode &&
                vm.drawingResourceState.mode === 'dim' &&
                vm.pmis &&
                vm.pmis.length > 0
              "
              class="inline-flex h-7 w-7 cursor-pointer items-center justify-center rounded-md text-white hover:bg-gray-700"
              [ngClass]="vm.showLegend ? 'bg-qw-primary-500' : 'bg-gray-400'"
              (click)="toggleLegend()"
            >
              <span title="Toggle Legend" class="text-sm font-bold">L</span>
            </div>
            <!-- Toggle Layout -->
            <div
              class="inline-flex h-7 w-7 cursor-pointer items-center justify-center rounded-md bg-gray-400 text-white hover:bg-gray-700"
              [ngClass]="{ 'rotate-90': vm.isFitToHeight }"
              (click)="toggleLayout()"
            >
              <fa-icon
                [icon]="faRulerHorizontal"
                [title]="vm.isFitToHeight ? 'Fit to width' : 'Fit to height'"
                class="text-sm"
              ></fa-icon>
            </div>
            <!-- New Comment -->
            <div
              *ngIf="!vm.inspectionPlanBuilderMode"
              class="inline-flex h-7 w-7 cursor-pointer items-center justify-center rounded-md bg-gray-400 text-white hover:bg-gray-700"
              (click)="openDiscussionStarterDialog()"
            >
              <fa-icon
                i18n-title
                [icon]="faSquarePlus"
                title="New Discussion"
                class="text-sm"
              ></fa-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Drawing display area -->
    <div
      *ngIf="!!vm.currentPageUrl"
      class="h-full w-full overflow-hidden border-x border-b border-gray-200"
    >
      <div
        class="h-full w-full"
        [ngClass]="{
          'overflow-x-auto': vm.isFitToHeight,
          'overflow-y-auto': !vm.isFitToHeight,
        }"
      >
        <div
          [ngClass]="{
            'h-full': vm.isFitToHeight,
            'w-full': !vm.isFitToHeight,
          }"
        >
          <qw-viewer-drawing-page
            [imgUrl]="vm.currentPageUrl"
            [isFitToHeight]="vm.isFitToHeight"
            [pmis]="vm.pmis"
            [discussionQuads]="vm.discussionQuads"
            [selectedPageIndex]="vm.currentPageIndex"
            [fileResourceRevisionId]="vm.currentRevisionId"
            [showLegend]="
              vm.showLegend && vm.drawingResourceState.mode === 'dim'
            "
          ></qw-viewer-drawing-page>
        </div>
      </div>
    </div>
  </div>
  <qw-viewer-drawing-discussion-starter
    *ngIf="vm.discussionMode"
  ></qw-viewer-drawing-discussion-starter>
</ng-container>
