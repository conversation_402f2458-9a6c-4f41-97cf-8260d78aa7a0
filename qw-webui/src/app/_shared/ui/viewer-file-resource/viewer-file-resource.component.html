<!-- We enable revisions only for fileType === 'drawing' -->
<!-- However currently revisions are not implemented for drawings either -->
<ng-container *ngIf="vm$ | async as vm">
  <qw-viewer-collabora
    *ngIf="vm.fileType === 'collabora'"
    class="h-full w-full"
    [revisionData]="{
      fileResourceRevision: vm.resource.revisions[0],
      displayName: vm.resource.displayName,
    }"
  ></qw-viewer-collabora>

  <ng-container *ngIf="vm.fileType === 'technicalDrawing'">
    <qw-viewer-drawing
      class="h-full w-full p-2"
      [selectedTechnicalDrawingResource]="vm.resource"
      [options]="{
        showStartDrawingDiscussionButton: true,
        hideVersionsIfOnlyOne: true,
      }"
    ></qw-viewer-drawing>
  </ng-container>

  <ng-container *ngIf="vm.fileType === 'materialCertificate'">
    <qw-viewer-material-certificate
      class="h-full w-full p-2"
      [selectedCertificate]="vm.resource"
    ></qw-viewer-material-certificate>
  </ng-container>

  <!-- qw-viewer-image is being used in multiple places -->
  <!-- the layout can be customized by passing custom classes  -->
  <qw-viewer-image
    *ngIf="vm.fileType === 'image'"
    class="h-full w-full items-center justify-center"
    layoutClass="flex h-full w-full"
    [imgUrl]="getFileRevisionUrl(vm.resource.revisions[0])"
  ></qw-viewer-image>

  <video
    controls
    *ngIf="vm.fileType === 'video'"
    class="h-full w-full"
    [src]="getFileRevisionUrl(vm.resource.revisions[0])"
  ></video>

  <div i18n *ngIf="vm.fileType === 'other'">Cannot display file</div>
</ng-container>
