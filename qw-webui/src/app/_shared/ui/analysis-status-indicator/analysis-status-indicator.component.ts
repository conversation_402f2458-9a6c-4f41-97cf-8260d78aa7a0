import { Ng<PERSON><PERSON>, NgIf } from "@angular/common"
import { Component, computed, input } from "@angular/core"
import { FaIconComponent } from "@fortawesome/angular-fontawesome"
import {
  faCircleCheck,
  faCircleExclamation,
  faClock,
} from "@fortawesome/free-solid-svg-icons"

type AnalysisStatus = null | "PENDING" | "ANALYZING" | "COMPLETED" | "FAILURE"

@Component({
  selector: "qw-analysis-status-indicator",
  templateUrl: "./analysis-status-indicator.component.html",
  standalone: true,
  imports: [NgClass, NgIf, FaIconComponent],
})
export class AnalysisStatusIndicatorComponent {
  status = input<AnalysisStatus>(null)
  showText = input<boolean>(false)
  message = input<string | null>(null)

  // FontAwesome icons
  protected readonly faCircleCheck = faCircleCheck
  protected readonly faCircleExclamation = faCircleExclamation
  protected readonly faClock = faClock

  protected readonly statusIcon = computed(() => {
    const status = this.status()
    switch (status) {
      case "PENDING":
      case "ANALYZING":
        return this.faClock
      case "COMPLETED":
        return this.faCircleCheck
      case "FAILURE":
        return this.faCircleExclamation
      default:
        return this.faCircleExclamation
    }
  })

  protected readonly statusIconClass = computed(() => {
    const status = this.status()
    switch (status) {
      case "PENDING":
      case "ANALYZING":
        return "text-orange-400"
      case "COMPLETED":
        return "text-qw-primary-500"
      case "FAILURE":
        return "text-qw-warn-500"
      default:
        return "text-qw-warn-500"
    }
  })

  protected readonly displayText = computed(() => {
    const customMessage = this.message()
    if (customMessage) return customMessage

    const status = this.status()
    switch (status) {
      case "PENDING":
        return $localize`Analysis queued`
      case "ANALYZING":
        return $localize`Analysis running`
      case "COMPLETED":
        return $localize`Analysis complete`
      case "FAILURE":
        return $localize`Analysis failed`
      default:
        return $localize`No analysis available`
    }
  })
}
