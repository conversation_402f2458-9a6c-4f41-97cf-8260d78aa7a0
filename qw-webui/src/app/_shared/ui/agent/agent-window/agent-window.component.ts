import { CommonModule } from "@angular/common"
import {
  Component,
  ElementRef,
  HostListener,
  inject,
  ViewChild,
} from "@angular/core"
import { FormsModule } from "@angular/forms"
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome"
import {
  faCircleNotch,
  faPaperPlane,
  faSearch,
  faTimes,
} from "@fortawesome/free-solid-svg-icons"
import { AgentService, MessageRole } from "@qw-shared-services/agent.service"
import { BehaviorSubject, combineLatest } from "rxjs"

@Component({
  selector: "qw-agent-window",
  templateUrl: "./agent-window.component.html",
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule],
})
export class AgentWindowComponent {
  private agentService = inject(AgentService)

  // Icons
  protected readonly faTimes = faTimes
  protected readonly faPaperPlane = faPaperPlane
  protected readonly faCircleNotch = faCircleNotch
  protected readonly faSearch = faSearch
  protected readonly MessageRole = MessageRole

  // UI state
  private userMessageSubject = new BehaviorSubject<string>("")

  // Create a single vm$ observable for the template
  vm$ = combineLatest({
    isVisible: this.agentService.isVisible$,
    messages: this.agentService.messages$,
    isLoading: this.agentService.isLoading$,
    userMessage: this.userMessageSubject,
  })

  // ViewChild references
  @ViewChild("messageContainer") messageContainer?: ElementRef<HTMLDivElement>
  @ViewChild("messageInput") messageInput?: ElementRef<HTMLTextAreaElement>

  // Handle Ctrl+K and Ctrl+Space keyboard shortcut
  @HostListener("document:keydown", ["$event"])
  handleKeyDown(event: KeyboardEvent): void {
    // Toggle chat window on Ctrl+K or Ctrl+Space
    if (
      event.ctrlKey &&
      (event.key === "k" || event.code === "Space" || event.key === " ")
    ) {
      event.preventDefault()
      this.agentService.toggle()

      // Focus the input field when opening
      if (this.agentService.isVisible()) {
        setTimeout(() => {
          this.messageInput?.nativeElement.focus()
        }, 0)
      }
    }

    // Close on Escape
    if (event.key === "Escape" && this.agentService.isVisible()) {
      event.preventDefault()
      this.agentService.hide()
    }
  }

  // Window management
  close(): void {
    this.agentService.hide()
  }

  sendMessage(): void {
    const message = this.userMessageSubject.value
    if (!message.trim()) return

    this.agentService.sendMessage(message)
    this.userMessageSubject.next("")

    setTimeout(() => {
      this.messageInput?.nativeElement.focus()
      this.scrollToBottom()
    }, 0)
  }

  updateUserMessage(value: string): void {
    this.userMessageSubject.next(value)
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      this.sendMessage()
    }
  }

  scrollToBottom(): void {
    setTimeout(() => {
      if (this.messageContainer) {
        const element = this.messageContainer.nativeElement
        element.scrollTop = element.scrollHeight
      }
    }, 0)
  }
}
