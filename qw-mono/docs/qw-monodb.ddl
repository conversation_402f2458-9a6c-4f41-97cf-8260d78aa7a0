-- this ddl is autogenerated
CREATE TYPE drawingfileanalysisstatus AS ENUM ('ANALYZING', 'COMPLETED', 'FAILURE', 'PENDING');

CREATE TYPE measurementunit AS ENUM ('MILLIMETER', 'MICROMETER', 'DEGREE');

CREATE TYPE gdtsymbol AS ENUM ('FLATNESS', 'STRAIGHTNESS', '<PERSON><PERSON><PERSON><PERSON>RI<PERSON>', '<PERSON>Y<PERSON>INDRICITY', 'PROFILE_OF_LINE', 'PROFILE_OF_SURFACE', 'PERPENDICULARITY', 'ANGULARITY', 'PARALLELISM', 'CONCENTRICITY', 'SYMMETRY', 'POSITION', 'CIRCULAR_RUNOUT', 'TOTAL_RUNOUT');

CREATE TYPE roughnesscategory AS ENUM ('RA', 'RZ', 'RY', 'RT', 'RQ');

CREATE TYPE materialcertificateanalysisstatus AS ENUM ('ANALYZING', 'COMPLETED', 'FAILURE', 'PENDING');

CREATE TYPE orderlinestatus AS ENUM ('ACTIVE', 'COMPLETED');

CREATE TABLE authentication (
	id SERIAL NOT NULL, 
	nonce VARCHAR NOT NULL, 
	finished BOOLEAN NOT NULL, 
	successful BOOLEAN NOT NULL, 
	ts_expiration TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	user_agent_redirect VARCHAR, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_measurement_diameter_detail (
	id SERIAL NOT NULL, 
	iso286_identifier VARCHAR, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_measurement_thread_detail (
	id SERIAL NOT NULL, 
	pitch FLOAT, 
	iso965_identifier VARCHAR, 
	thread_standard VARCHAR, 
	depth FLOAT, 
	depth_unit measurementunit, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_measurement_gdt_detail (
	id SERIAL NOT NULL, 
	symbol gdtsymbol, 
	datum_references VARCHAR, 
	material_condition VARCHAR, 
	diameter_modifier BOOLEAN NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_measurement_angle_detail (
	id SERIAL NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_measurement_linear_detail (
	id SERIAL NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_measurement_radius_detail (
	id SERIAL NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_measurement_chamfer_detail (
	id SERIAL NOT NULL, 
	angle FLOAT, 
	length FLOAT, 
	length_unit measurementunit, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_measurement_roughness_detail (
	id SERIAL NOT NULL, 
	roughness_category roughnesscategory, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_plan_action_image (
	id SERIAL NOT NULL, 
	optional BOOLEAN NOT NULL, 
	instruction VARCHAR, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_plan_action_video (
	id SERIAL NOT NULL, 
	optional BOOLEAN NOT NULL, 
	instruction VARCHAR, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE inspection_plan_action_question_answer (
	id SERIAL NOT NULL, 
	optional BOOLEAN NOT NULL, 
	question VARCHAR NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE s3_object_reference (
	id SERIAL NOT NULL, 
	bucket VARCHAR NOT NULL, 
	name VARCHAR NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT full_object_name UNIQUE (bucket, name)
);

CREATE TABLE purchase_order_info (
	id SERIAL NOT NULL, 
	supplier_tenant_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE production_order_info (
	id SERIAL NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE "user" (
	id SERIAL NOT NULL, 
	issuer VARCHAR NOT NULL, 
	subject VARCHAR NOT NULL, 
	given_name VARCHAR NOT NULL, 
	family_name VARCHAR NOT NULL, 
	email VARCHAR NOT NULL, 
	tenant_id INTEGER, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT issuer_and_subject UNIQUE (issuer, subject)
);

CREATE TABLE session (
	id SERIAL NOT NULL, 
	uuid VARCHAR NOT NULL, 
	authentication_id INTEGER NOT NULL, 
	issuer VARCHAR NOT NULL, 
	subject VARCHAR NOT NULL, 
	ts_expiration_access TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_expiration_refresh TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	token_id VARCHAR NOT NULL, 
	token_access VARCHAR NOT NULL, 
	token_refresh VARCHAR NOT NULL, 
	was_logged_out BOOLEAN NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	UNIQUE (uuid), 
	FOREIGN KEY(authentication_id) REFERENCES authentication (id)
);

CREATE TABLE chat (
	id SERIAL NOT NULL, 
	creator_user_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id)
);

CREATE TABLE inspection_plan (
	id SERIAL NOT NULL, 
	title VARCHAR NOT NULL, 
	creator_user_id INTEGER NOT NULL, 
	owner_tenant_id INTEGER NOT NULL, 
	is_readonly BOOLEAN NOT NULL, 
	ts_edit TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	deleted BOOLEAN NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id)
);

CREATE TABLE inspection_plan_action_measurement (
	id SERIAL NOT NULL, 
	optional BOOLEAN NOT NULL, 
	value_expected FLOAT NOT NULL, 
	value_unit measurementunit NOT NULL, 
	value_lower_tolerance FLOAT, 
	value_upper_tolerance FLOAT, 
	importance VARCHAR NOT NULL, 
	result_type VARCHAR NOT NULL, 
	tolerance_source VARCHAR, 
	count INTEGER NOT NULL, 
	raw_callout VARCHAR, 
	diameter_detail_id INTEGER, 
	thread_detail_id INTEGER, 
	gdt_detail_id INTEGER, 
	angle_detail_id INTEGER, 
	linear_detail_id INTEGER, 
	radius_detail_id INTEGER, 
	chamfer_detail_id INTEGER, 
	roughness_detail_id INTEGER, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT at_max_one_measurement_detail CHECK ((CASE WHEN diameter_detail_id IS NULL THEN 0 ELSE 1 END + CASE WHEN thread_detail_id IS NULL THEN 0 ELSE 1 END + CASE WHEN gdt_detail_id IS NULL THEN 0 ELSE 1 END + CASE WHEN angle_detail_id IS NULL THEN 0 ELSE 1 END + CASE WHEN linear_detail_id IS NULL THEN 0 ELSE 1 END + CASE WHEN radius_detail_id IS NULL THEN 0 ELSE 1 END + CASE WHEN chamfer_detail_id IS NULL THEN 0 ELSE 1 END + CASE WHEN roughness_detail_id IS NULL THEN 0 ELSE 1 END) < 2), 
	FOREIGN KEY(diameter_detail_id) REFERENCES inspection_measurement_diameter_detail (id), 
	FOREIGN KEY(thread_detail_id) REFERENCES inspection_measurement_thread_detail (id), 
	FOREIGN KEY(gdt_detail_id) REFERENCES inspection_measurement_gdt_detail (id), 
	FOREIGN KEY(angle_detail_id) REFERENCES inspection_measurement_angle_detail (id), 
	FOREIGN KEY(linear_detail_id) REFERENCES inspection_measurement_linear_detail (id), 
	FOREIGN KEY(radius_detail_id) REFERENCES inspection_measurement_radius_detail (id), 
	FOREIGN KEY(chamfer_detail_id) REFERENCES inspection_measurement_chamfer_detail (id), 
	FOREIGN KEY(roughness_detail_id) REFERENCES inspection_measurement_roughness_detail (id)
);

CREATE TABLE inspection_finish (
	id SERIAL NOT NULL, 
	count_non_conform_measurements INTEGER NOT NULL, 
	creator_user_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id)
);

CREATE TABLE file_resource (
	id SERIAL NOT NULL, 
	uuid VARCHAR NOT NULL, 
	display_name VARCHAR NOT NULL, 
	label VARCHAR NOT NULL, 
	mime_type VARCHAR NOT NULL, 
	undeleted_revision_count INTEGER NOT NULL, 
	creator_user_id INTEGER NOT NULL, 
	owner_tenant_id INTEGER NOT NULL, 
	deleted BOOLEAN NOT NULL, 
	is_versioned BOOLEAN NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	UNIQUE (uuid), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id)
);

CREATE TABLE "order" (
	id SERIAL NOT NULL, 
	customer_tenant_id INTEGER NOT NULL, 
	customer_internal_reference VARCHAR NOT NULL, 
	order_date DATE NOT NULL, 
	creator_user_id INTEGER NOT NULL, 
	deleted BOOLEAN NOT NULL, 
	purchase_order_info_id INTEGER, 
	production_order_info_id INTEGER, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT one_order_info CHECK ((CASE WHEN purchase_order_info_id IS NULL THEN 0 ELSE 1 END + CASE WHEN production_order_info_id IS NULL THEN 0 ELSE 1 END) = 1), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id), 
	FOREIGN KEY(purchase_order_info_id) REFERENCES purchase_order_info (id), 
	FOREIGN KEY(production_order_info_id) REFERENCES production_order_info (id)
);

CREATE TABLE acl_file_resource (
	id SERIAL NOT NULL, 
	file_resource_id INTEGER NOT NULL, 
	tenant_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_acl_file_resource UNIQUE (file_resource_id, tenant_id), 
	FOREIGN KEY(file_resource_id) REFERENCES file_resource (id)
);

CREATE TABLE acl_inspection_plan (
	id SERIAL NOT NULL, 
	inspection_plan_id INTEGER NOT NULL, 
	tenant_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_acl_inspection_plan UNIQUE (inspection_plan_id, tenant_id), 
	FOREIGN KEY(inspection_plan_id) REFERENCES inspection_plan (id)
);

CREATE TABLE acl_chat (
	id SERIAL NOT NULL, 
	chat_id INTEGER NOT NULL, 
	tenant_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(chat_id) REFERENCES chat (id)
);

CREATE TABLE chat_message (
	id SERIAL NOT NULL, 
	chat_id INTEGER NOT NULL, 
	creator_user_id INTEGER NOT NULL, 
	latest_content_id INTEGER, 
	edit_count INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(chat_id) REFERENCES chat (id), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id)
);

CREATE TABLE inspection (
	id SERIAL NOT NULL, 
	uuid VARCHAR NOT NULL, 
	inspection_plan_id INTEGER NOT NULL, 
	sample_count INTEGER NOT NULL, 
	finish_id INTEGER, 
	creator_user_id INTEGER NOT NULL, 
	creator_tenant_id INTEGER NOT NULL, 
	inspector_tenant_id INTEGER NOT NULL, 
	deleted BOOLEAN NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	UNIQUE (uuid), 
	FOREIGN KEY(inspection_plan_id) REFERENCES inspection_plan (id), 
	FOREIGN KEY(finish_id) REFERENCES inspection_finish (id), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id)
);

CREATE TABLE material (
	id SERIAL NOT NULL, 
	owner_tenant_id INTEGER NOT NULL, 
	owner_internal_reference VARCHAR NOT NULL, 
	name VARCHAR NOT NULL, 
	creator_user_id INTEGER NOT NULL, 
	chat_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_internal_material_reference UNIQUE (owner_tenant_id, owner_internal_reference), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id), 
	FOREIGN KEY(chat_id) REFERENCES chat (id)
);

CREATE TABLE file_resource_revision (
	id SERIAL NOT NULL, 
	obj_id INTEGER NOT NULL, 
	file_resource_id INTEGER NOT NULL, 
	revision_number INTEGER NOT NULL, 
	revision_is_latest BOOLEAN NOT NULL, 
	original_name VARCHAR NOT NULL, 
	hash VARCHAR NOT NULL, 
	size INTEGER NOT NULL, 
	page_count INTEGER, 
	creator_user_id INTEGER NOT NULL, 
	deleted BOOLEAN NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT only_one_version_number UNIQUE (file_resource_id, revision_number), 
	FOREIGN KEY(obj_id) REFERENCES s3_object_reference (id), 
	FOREIGN KEY(file_resource_id) REFERENCES file_resource (id), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id)
);

CREATE TABLE acl_file_resource_revision (
	id SERIAL NOT NULL, 
	file_resource_revision_id INTEGER NOT NULL, 
	tenant_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_acl_file_resource_revision UNIQUE (file_resource_revision_id, tenant_id), 
	FOREIGN KEY(file_resource_revision_id) REFERENCES file_resource_revision (id)
);

CREATE TABLE acl_inspection (
	id SERIAL NOT NULL, 
	inspection_id INTEGER NOT NULL, 
	tenant_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_acl_inspection UNIQUE (inspection_id, tenant_id), 
	FOREIGN KEY(inspection_id) REFERENCES inspection (id)
);

CREATE TABLE chat_message_content (
	id SERIAL NOT NULL, 
	chat_message_id INTEGER NOT NULL, 
	content VARCHAR NOT NULL, 
	ts_sent TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(chat_message_id) REFERENCES chat_message (id)
);

CREATE TABLE drawing_file_analysis (
	id SERIAL NOT NULL, 
	file_resource_revision_id INTEGER NOT NULL, 
	page_index INTEGER, 
	analysis_obj_id INTEGER, 
	analysis_status drawingfileanalysisstatus NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_drawing_file_analysis UNIQUE (file_resource_revision_id, page_index), 
	FOREIGN KEY(file_resource_revision_id) REFERENCES file_resource_revision (id), 
	FOREIGN KEY(analysis_obj_id) REFERENCES s3_object_reference (id)
);

CREATE TABLE drawing_file_page (
	id SERIAL NOT NULL, 
	file_resource_revision_id INTEGER NOT NULL, 
	page_index INTEGER NOT NULL, 
	image_obj_id INTEGER NOT NULL, 
	thumbnail_obj_id INTEGER, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_drawing_file_page UNIQUE (file_resource_revision_id, page_index), 
	FOREIGN KEY(file_resource_revision_id) REFERENCES file_resource_revision (id), 
	FOREIGN KEY(image_obj_id) REFERENCES s3_object_reference (id), 
	FOREIGN KEY(thumbnail_obj_id) REFERENCES s3_object_reference (id)
);

CREATE TABLE drawing_file_tile (
	id SERIAL NOT NULL, 
	file_resource_revision_id INTEGER NOT NULL, 
	page_index INTEGER NOT NULL, 
	dpi INTEGER NOT NULL, 
	tile_x INTEGER NOT NULL, 
	tile_y INTEGER NOT NULL, 
	tile_obj_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_drawing_file_tile UNIQUE (file_resource_revision_id, page_index, dpi, tile_x, tile_y), 
	FOREIGN KEY(file_resource_revision_id) REFERENCES file_resource_revision (id), 
	FOREIGN KEY(tile_obj_id) REFERENCES s3_object_reference (id)
);

CREATE TABLE drawing_file_normalized_quad (
	id SERIAL NOT NULL, 
	file_resource_revision_id INTEGER NOT NULL, 
	page_index INTEGER NOT NULL, 
	x1 FLOAT NOT NULL, 
	y1 FLOAT NOT NULL, 
	x2 FLOAT NOT NULL, 
	y2 FLOAT NOT NULL, 
	x3 FLOAT NOT NULL, 
	y3 FLOAT NOT NULL, 
	x4 FLOAT NOT NULL, 
	y4 FLOAT NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(file_resource_revision_id) REFERENCES file_resource_revision (id)
);

CREATE TABLE link_material_file_resource_revision (
	id SERIAL NOT NULL, 
	material_id INTEGER NOT NULL, 
	file_resource_revision_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_file_revision_per_material UNIQUE (material_id, file_resource_revision_id), 
	FOREIGN KEY(material_id) REFERENCES material (id), 
	FOREIGN KEY(file_resource_revision_id) REFERENCES file_resource_revision (id)
);

CREATE TABLE material_certificate_analysis (
	id SERIAL NOT NULL, 
	file_resource_revision_id INTEGER NOT NULL, 
	analysis_obj_id INTEGER, 
	analysis_status materialcertificateanalysisstatus NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(file_resource_revision_id) REFERENCES file_resource_revision (id), 
	FOREIGN KEY(analysis_obj_id) REFERENCES s3_object_reference (id)
);

CREATE TABLE material_certificate_page (
	id SERIAL NOT NULL, 
	file_resource_revision_id INTEGER NOT NULL, 
	page_index INTEGER NOT NULL, 
	image_obj_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_material_certificate_page UNIQUE (file_resource_revision_id, page_index), 
	FOREIGN KEY(file_resource_revision_id) REFERENCES file_resource_revision (id), 
	FOREIGN KEY(image_obj_id) REFERENCES s3_object_reference (id)
);

CREATE TABLE order_line (
	id SERIAL NOT NULL, 
	order_id INTEGER NOT NULL, 
	customer_material_id INTEGER NOT NULL, 
	customer_internal_reference VARCHAR NOT NULL, 
	count INTEGER NOT NULL, 
	expected_delivery_date DATE NOT NULL, 
	status orderlinestatus NOT NULL, 
	creator_user_id INTEGER NOT NULL, 
	chat_id INTEGER NOT NULL, 
	deleted BOOLEAN NOT NULL, 
	requirements JSON NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(order_id) REFERENCES "order" (id), 
	FOREIGN KEY(customer_material_id) REFERENCES material (id), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id), 
	FOREIGN KEY(chat_id) REFERENCES chat (id)
);

CREATE TABLE link_tenant_file_resource_revision (
	id SERIAL NOT NULL, 
	tenant_id INTEGER NOT NULL, 
	file_resource_revision_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_file_revision_per_tenant UNIQUE (tenant_id, file_resource_revision_id), 
	FOREIGN KEY(file_resource_revision_id) REFERENCES file_resource_revision (id)
);

CREATE TABLE drawing_discussion (
	id SERIAL NOT NULL, 
	file_resource_id INTEGER NOT NULL, 
	quad_id INTEGER NOT NULL, 
	chat_id INTEGER NOT NULL, 
	resolved BOOLEAN NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(file_resource_id) REFERENCES file_resource (id), 
	FOREIGN KEY(quad_id) REFERENCES drawing_file_normalized_quad (id), 
	FOREIGN KEY(chat_id) REFERENCES chat (id)
);

CREATE TABLE inspection_plan_step (
	id SERIAL NOT NULL, 
	inspection_plan_id INTEGER NOT NULL, 
	step_index INTEGER NOT NULL, 
	drawing_quad_id INTEGER, 
	repeat_for_each_sample BOOLEAN NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_inspection_plan_index UNIQUE (inspection_plan_id, step_index), 
	FOREIGN KEY(inspection_plan_id) REFERENCES inspection_plan (id), 
	FOREIGN KEY(drawing_quad_id) REFERENCES drawing_file_normalized_quad (id)
);

CREATE TABLE link_order_line_file_resource_revision (
	id SERIAL NOT NULL, 
	order_line_id INTEGER NOT NULL, 
	file_resource_revision_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_file_revision_per_order_line UNIQUE (order_line_id, file_resource_revision_id), 
	FOREIGN KEY(order_line_id) REFERENCES order_line (id), 
	FOREIGN KEY(file_resource_revision_id) REFERENCES file_resource_revision (id)
);

CREATE TABLE link_order_line_inspection_plan (
	id SERIAL NOT NULL, 
	order_line_id INTEGER NOT NULL, 
	inspection_plan_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_inspection_plan_per_order_line UNIQUE (order_line_id, inspection_plan_id), 
	FOREIGN KEY(order_line_id) REFERENCES order_line (id), 
	FOREIGN KEY(inspection_plan_id) REFERENCES inspection_plan (id)
);

CREATE TABLE task_order_line_inspection (
	id SERIAL NOT NULL, 
	order_line_id INTEGER NOT NULL, 
	inspection_plan_id INTEGER NOT NULL, 
	inspection_id INTEGER NOT NULL, 
	creator_user_id INTEGER NOT NULL, 
	due_date DATE, 
	deleted BOOLEAN NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(order_line_id) REFERENCES order_line (id), 
	FOREIGN KEY(inspection_plan_id) REFERENCES inspection_plan (id), 
	FOREIGN KEY(inspection_id) REFERENCES inspection (id), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id)
);

CREATE TABLE inspection_plan_action (
	id SERIAL NOT NULL, 
	step_id INTEGER NOT NULL, 
	action_index INTEGER NOT NULL, 
	action_measurement_id INTEGER, 
	action_image_id INTEGER, 
	action_video_id INTEGER, 
	action_qna_id INTEGER, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT one_inspection_action_type CHECK ((CASE WHEN action_measurement_id IS NULL THEN 0 ELSE 1 END + CASE WHEN action_image_id IS NULL THEN 0 ELSE 1 END + CASE WHEN action_video_id IS NULL THEN 0 ELSE 1 END + CASE WHEN action_qna_id IS NULL THEN 0 ELSE 1 END) = 1), 
	FOREIGN KEY(step_id) REFERENCES inspection_plan_step (id), 
	FOREIGN KEY(action_measurement_id) REFERENCES inspection_plan_action_measurement (id), 
	FOREIGN KEY(action_image_id) REFERENCES inspection_plan_action_image (id), 
	FOREIGN KEY(action_video_id) REFERENCES inspection_plan_action_video (id), 
	FOREIGN KEY(action_qna_id) REFERENCES inspection_plan_action_question_answer (id)
);

CREATE TABLE inspection_action_result (
	id SERIAL NOT NULL, 
	inspection_id INTEGER NOT NULL, 
	inspection_plan_action_id INTEGER NOT NULL, 
	sample_index INTEGER, 
	measured_value FLOAT, 
	binary_value BOOLEAN, 
	image_resource_id INTEGER, 
	video_resource_id INTEGER, 
	answer_value VARCHAR, 
	creator_user_id INTEGER NOT NULL, 
	ts_created TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	ts_updated TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
	PRIMARY KEY (id), 
	CONSTRAINT one_inspection_action_result CHECK ((CASE WHEN measured_value IS NULL THEN 0 ELSE 1 END + CASE WHEN binary_value IS NULL THEN 0 ELSE 1 END + CASE WHEN image_resource_id IS NULL THEN 0 ELSE 1 END + CASE WHEN video_resource_id IS NULL THEN 0 ELSE 1 END + CASE WHEN answer_value IS NULL THEN 0 ELSE 1 END) = 1), 
	CONSTRAINT unique_inspection_action_result UNIQUE (inspection_id, inspection_plan_action_id, sample_index), 
	FOREIGN KEY(inspection_id) REFERENCES inspection (id), 
	FOREIGN KEY(inspection_plan_action_id) REFERENCES inspection_plan_action (id), 
	FOREIGN KEY(image_resource_id) REFERENCES s3_object_reference (id), 
	FOREIGN KEY(video_resource_id) REFERENCES s3_object_reference (id), 
	FOREIGN KEY(creator_user_id) REFERENCES "user" (id)
);

