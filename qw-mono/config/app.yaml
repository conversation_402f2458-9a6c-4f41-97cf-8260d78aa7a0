mono_db:
  schema_verification:
    type: liquibase
    valid_tags: ["0.4.11"]
  backend:
    type: postgres
    host: ${OVERWRITE}
    port: ${OVERWRITE}
    username: ${OVERWRITE}
    password: ${OVERWRITE}
    database_name: ${OVERWRITE}
    pool_size: 10
    pool_max_overflow: 15
    pool_timeout_seconds: 10

mono_s3:
  create_buckets_if_not_exist: ${OVERWRITE}
  backend:
    type: minio
    host: ${OVERWRITE}
    access_key: ${OVERWRITE}
    secret_key: ${OVERWRITE}
    secure: ${OVERWRITE}

mono_pfoertner:
  iam:
    type: "keycloak"
    host: ${OVERWRITE}
    initial_healthcheck:
      timeout_in_seconds: 5
      retry_count: -1
    realm: "qualiwise"
    openid_redirect_host: ${OVERWRITE}
    openid_redirect_route: "/api/v1/session/login/conclude"
    openid_client_id: ${OVERWRITE}
    openid_client_secret: ${OVERWRITE}
  tkn_gateway_salt: ${OVERWRITE}
  session_settings: {}
  wopi_auth_settings:
    jws_oct_key: ${OVERWRITE}
    jws_alg: ${OVERWRITE}
  policy_settings: {}
  settings:
    include_spec_route: ${OVERWRITE}

mono_trunk:
  chat_db_settings: {}
  drawing_settings: {}
  wopi_settings:
    wopi_client_host: ${OVERWRITE}
  api_keys:
    claude_api_key: ${OVERWRITE}
    openai_key: ${OVERWRITE}
    groq_api_key: ${OVERWRITE}
    gemini_api_key: ${OVERWRITE}
    mistral_api_key: ${OVERWRITE}

mono_worker:
  celery_broker_settings:
    type: rabbitmq
    host: ${OVERWRITE}
    port: ${OVERWRITE}
    user: ${OVERWRITE}
    password: ${OVERWRITE}
  celery_worker_settings:
    concurrency: ${OVERWRITE}
    max_tasks_per_child: ${OVERWRITE}
    loglevel: ${OVERWRITE}
  api_keys:
    claude_api_key: ${OVERWRITE}
    openai_key: ${OVERWRITE}
    groq_api_key: ${OVERWRITE}
    gemini_api_key: ${OVERWRITE}
    mistral_api_key: ${OVERWRITE}

mono_agent_service:
  runtime_service_url: ${OVERWRITE}
  api_keys:
    claude_api_key: ${OVERWRITE}
    openai_key: ${OVERWRITE}
    groq_api_key: ${OVERWRITE}
    gemini_api_key: ${OVERWRITE}
    mistral_api_key: ${OVERWRITE}
  mcp_server_url: ${OVERWRITE}

mono_mcp_server:
  host: ${OVERWRITE}
  port: ${OVERWRITE}
  transport: ${OVERWRITE}
  api_base_url: ${OVERWRITE}
  openapi_spec_path: ${OVERWRITE}

tenants:
  directory: ${DIR}/qw-tenant-config
  validate_on_load: yes

logging:
  level: "INFO"
  logfire:
    enabled: ${OVERWRITE}
    token: ${OVERWRITE}
    instrument_pydantic_ai: true
    environment: ${OVERWRITE}
