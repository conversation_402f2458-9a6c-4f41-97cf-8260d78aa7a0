#!/usr/bin/env sh

# Ensure docs folder exists in the mounted code before starting
# if [ ! -d "/home/<USER>/repo/qw-mono/src/docs" ]; then
#   mkdir -p /home/<USER>/repo/qw-mono/src/docs
#   cp -r /home/<USER>/repo/qw-mono/docs/* /home/<USER>/repo/qw-mono/src/docs/
# fi

FILE=$(readlink -f "$0")
FILE_DIR=$(dirname "$FILE")
PROJECT_DIR=$(realpath $FILE_DIR/..)
REPO_DIR=$(realpath $PROJECT_DIR/..)

export PYTHONMALLOC=malloc
export PYTHONUNBUFFERED=1

cd ${PROJECT_DIR}/src
python3 -m qw_mcp_server.mcp_server_entrypoint \
  --qw-mono-config "${REPO_DIR}/dev_data/app.yaml"
