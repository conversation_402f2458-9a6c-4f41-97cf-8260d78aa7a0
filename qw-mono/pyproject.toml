[tool.poetry]
name = "qw-mono"
version = "0.1.0"
packages = [{ include = "qw_mono", from = "src" }]

[tool.poetry.dependencies]
python = "~3.10"
falcon = "3.1.1"
gunicorn = "21.2.0"
joserfc = "0.9.0"
minio = "7.1.15"
numpy = "1.26.1"
jinja2 = "3.1.4"
pydantic = ">=2.10,<3.0"
PyYAML = "^6.0.2"
SQLAlchemy = "2.0.19"
xsdata = "24.3.1"
opencv-python-headless = "4.10.0.84"
PyMuPDF = "1.24.9"
Pillow = "10.4.0"
psycopg2 = "2.9.9"
rapidocr-onnxruntime = "^1.4.4"
pydantic-ai = "^0.3.2"
requests = "^2.32.3"
google-generativeai = "^0.8.4"
google-cloud-vision = "^3.10.1"
celery = "^5.5.1"
logfire = { extras = ["httpx"], version = "^3.15.0" }
weasyprint = "^65.1"
ocrmypdf = "^16.10.2"
fastapi = "^0.115.12"
httpx = "^0.28.1"
uvicorn = "^0.34.3"
fastmcp = "^2.8.0"

[tool.poetry.group.dev.dependencies]
mypy = "1.15.0"
mypy-extensions = "^1.0.0"
pytest = "7.4.0"
types-PyYAML = "6.0.12.11"
types-urllib3 = "1.26.25.14"
types-requests = "^2.32.0.20250328"
scrapy = "^2.12.0"
html2text = "^2024.2.26"
beautifulsoup4 = "^4.13.3"
# manual pre-commit modules until qw-mono is moved into separate repo
black = "23.7.0"
isort = "5.12.0"
flake8 = "6.0.0"
pep8-naming = "0.13.3"
flake8-absolute-import = "1.0.0.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"

[tool.black]
line-length = 120

[tool.isort]
profile = "black"
line_length = 120

[tool.mypy]
python_version = "3.10"
strict = true
plugins = ["pydantic.mypy", "numpy.typing.mypy_plugin"]

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = false

[[tool.mypy.overrides]]
module = "falcon.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "joserfc.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = ["celery.*"]
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "minio.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "pytest.*"
ignore_missing_imports = true


[[tool.mypy.overrides]]
module = "fitz.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "pymupdf.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "rapidocr_onnxruntime"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "weasyprint.*"
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
pythonpath = ["src"]
testpaths = ["src/*_test"]
python_files = "*.py"
log_cli = true
log_cli_level = "INFO"
