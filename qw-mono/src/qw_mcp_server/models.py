"""Configuration models for MCP Server following qw-mono patterns."""
from typing import Literal

from pydantic import BaseModel

from qw_basic_rdb.builder.base import RelationalDatabaseConfig
from qw_basic_s3.builder import S3StorageConfig
from qw_log.factory import QwLogConfig
from qw_tenant_config.registry import QwTenantsConfig


class QwMcpServerBaseConfig(BaseModel):
    """Base configuration for the MCP server."""

    host: str
    port: int
    transport: Literal["stdio", "sse", "streamable-http"]
    api_base_url: str
    openapi_spec_path: str


class QwMcpServerConfig(QwMcpServerBaseConfig):
    """MCP server configuration.

    This class combines all the configuration needed for the MCP server.
    """

    s3: S3StorageConfig
    db: RelationalDatabaseConfig
    tenants: QwTenantsConfig


class QwMcpServerMonoConfig(BaseModel):
    """Configuration for the MCP server in the mono app.

    This class is used to load the configuration from app.yaml.
    """

    mono_mcp_server: QwMcpServerBaseConfig
    mono_s3: S3StorageConfig
    mono_db: RelationalDatabaseConfig
    tenants: QwTenantsConfig
    logging: QwLogConfig

    @property
    def mcp_server(self) -> QwMcpServerConfig:
        """Get the MCP server configuration.

        Returns:
            The MCP server configuration
        """
        return QwMcpServerConfig(
            host=self.mono_mcp_server.host,
            port=self.mono_mcp_server.port,
            transport=self.mono_mcp_server.transport,
            api_base_url=self.mono_mcp_server.api_base_url,
            openapi_spec_path=self.mono_mcp_server.openapi_spec_path,
            s3=self.mono_s3,
            db=self.mono_db,
            tenants=self.tenants,
        )
