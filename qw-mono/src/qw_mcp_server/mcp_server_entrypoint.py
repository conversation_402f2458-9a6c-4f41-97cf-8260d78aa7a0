#!/usr/bin/env python3
"""
MCP Server Standalone Entrypoint

This script runs the MCP server as a standalone application in a separate container.
It loads the configuration and starts the MCP server to expose internal APIs as MCP tools.
"""

import argparse
import os
import sys
from pathlib import Path

from qw_config.loader import load_config
from qw_log.factory import QwLogFactory
from qw_mcp_server.fastmcp_server import FastMCPServerService
from qw_mcp_server.models import QwMcpServerMonoConfig
from qw_mono.app import QwAppInfo


def main() -> None:
    """Main entrypoint for the MCP server."""
    parser = argparse.ArgumentParser("mcp-server")
    parser.add_argument("--qw-mono-config", metavar="FILE", type=Path, required=True)
    parser.add_argument("--qw-mono-overwrite-config", metavar="FILE", type=Path, default=None)

    args = parser.parse_args()

    try:
        # Load the MCP server mono configuration using qw-mono config loader
        mcp_mono_cfg = load_config(QwMcpServerMonoConfig, args.qw_mono_config, args.qw_mono_overwrite_config)

        # Get version and commit from environment (same pattern as main app)
        version = os.environ.get("BUILD_VERSION", "0.0.0.0")
        commit = os.environ.get("BUILD_COMMIT", "0" * 40)

        print(f"DEBUG: Using version='{version}', commit='{commit}'")

        # Initialize logging
        app_info = QwAppInfo(
            name="qw-mcp-server",
            version=version,
            commit=commit,
        )
        log_factory = QwLogFactory(app_info.version)
        log_factory.init_logs(mcp_mono_cfg.logging)
        logger = log_factory.get_logger(__name__)

        logger.info("Starting MCP server standalone")

        # Get the MCP server configuration
        mcp_config = mcp_mono_cfg.mcp_server

        # Create and run FastMCP server
        mcp_server = FastMCPServerService(mcp_config, log_factory)
        logger.info(f"FastMCP server configured for {mcp_config.host}:{mcp_config.port}")

        # Run the server (this will block)
        mcp_server.run_server()

    except KeyboardInterrupt:
        print("\nMCP server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"Failed to start MCP server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
