import argparse
import os
from pathlib import Path
from typing import Literal, Type

import pytest
from pydantic import AnyHttpUrl
from sqlalchemy.orm import DeclarativeBase

from qw_basic_iam_policy.policy import QwPolicy, QwPolicySettings
from qw_basic_keycloak.impl.memory.iam import MemoryIamConfig
from qw_basic_keycloak.openid.session import SessionTokenGateway
from qw_basic_rdb.builder.postgres import PostgreSqlConfig
from qw_basic_rdb.devutil import RdbMode, RdbTestFactory
from qw_basic_rdb.interface import RelationalDatabase
from qw_basic_s3.devutil import S3Mode, S3TestFactory
from qw_basic_s3.impl.minio import MinioS3StorageConfig
from qw_basic_s3.interface import S3Storage
from qw_basic_s3.schema import S3StorageSchema
from qw_log.factory import QwLogFactory
from qw_log_interface import LogFactory
from qw_mono.app import QwAppIn<PERSON>, Qw<PERSON><PERSON>App
from qw_mono_test.app import QwMonoAuthClientFactory, QwMonoTestApp, QwMonoTestClient
from qw_monodb.table.base import Base
from qw_pfoertner.config import QwPfoertnerSettings
from qw_pfoertner.module import QwPfoertnerModule
from qw_pfoertner.service.session import SessionService, SessionServiceSettings
from qw_pfoertner.service.wopi_auth import WopiAuthenticationService, WopiAuthenticationServiceSettings
from qw_tenant_config.registry import TenantConfigRegistry
from qw_trunk.config import QwTrunkApiKeys
from qw_trunk.module import QwTrunkModule
from qw_trunk.service.drawing.drawing import DrawingServiceSettings
from qw_trunk.service.wopi import WopiServiceSettings
from qw_trunk_test.compose_file import QwComposeFile

REPO = Path(__file__).parents[3]
PROJECT = REPO / "qw-mono"
COMPOSE_FILE = QwComposeFile.load(REPO / "docker-compose.yml")
IN_RUNTIME = os.environ.get("HOSTNAME") == COMPOSE_FILE.get_runtime_host()
OPT_UPDATE_SPEC = "--store-openapi-spec"
OPT_UPDATE_DDL = "--store-database-ddl"
SCOPE: Literal["module"] = "module"


def pytest_addoption(parser: pytest.Parser) -> None:
    parser.addoption(
        OPT_UPDATE_SPEC,
        action=argparse.BooleanOptionalAction,
        help="overwrite the openapi spec file",
    )
    parser.addoption(
        OPT_UPDATE_DDL,
        action=argparse.BooleanOptionalAction,
        help="overwrite the database ddl file(s)",
    )


def create_rdb(name: str, base_type: Type[DeclarativeBase]) -> RelationalDatabase:
    # mode = RdbMode(request.config.getoption(OPT_RDB))
    mode = RdbMode.IN_MEMORY
    return RdbTestFactory(
        base=base_type,
        postgres_cfg=PostgreSqlConfig(
            host=COMPOSE_FILE.get_postgres_host(IN_RUNTIME),
            port=COMPOSE_FILE.get_postgres_port(IN_RUNTIME),
            username=COMPOSE_FILE.get_postgres_credentials()[0],
            password=COMPOSE_FILE.get_postgres_credentials()[1],
            database_name=name,
        ),
        mode=mode,
    ).create()


def create_s3(schema: S3StorageSchema) -> S3Storage:
    # mode = S3Impl(request.config.getoption(OPT_S3))
    mode = S3Mode.IN_MEMORY
    return S3TestFactory(
        bucket_prefix="qw-mono-test",
        minio_cfg=MinioS3StorageConfig(
            host=COMPOSE_FILE.get_minio_host_and_port(IN_RUNTIME),
            access_key=COMPOSE_FILE.get_postgres_credentials()[0],
            secret_key=COMPOSE_FILE.get_postgres_credentials()[1],
            secure=False,
        ),
        mode=mode,
    ).create(schema)


@pytest.fixture(scope=SCOPE)
def app_version() -> str:
    return "0.0.0.0"


@pytest.fixture(scope=SCOPE)
def log_factory(app_version: str) -> LogFactory:
    lf = QwLogFactory(app_version)
    lf.init_logs()
    return lf


@pytest.fixture(scope=SCOPE)
def tenants(log_factory: LogFactory) -> TenantConfigRegistry:
    p = REPO / "dev_data" / "tenants"
    return TenantConfigRegistry.read_from_directory(p, validate=True, lf=log_factory)


@pytest.fixture(scope="function")
def mono(app_version: str, log_factory: LogFactory, tenants: TenantConfigRegistry) -> QwMonoTestApp:
    keycloak_path = REPO / "dev_data" / "keycloak"
    iam = MemoryIamConfig(
        keycloak_realm_file=keycloak_path / "qualiwise.json",
        keycloak_realm_secrets_file=keycloak_path / "qualiwise-secrets.json",
        keycloak_host=AnyHttpUrl("https://identity.dev.qualiwise.eu"),
        openid_redirect_uri=AnyHttpUrl("https://identity.dev.qualiwise.eu/api/v1/session/login/conclude"),
        openid_client_id="qualiwise-pfoertner",
    ).build(lf=log_factory)

    rdb = create_rdb("monodb", Base)
    pfoertner_rdb = rdb
    trunk_rdb = rdb
    trunk_s3 = create_s3(schema=S3StorageSchema.create_simple(tenants.get_tenant_buckets()))
    trunk_module = QwTrunkModule.build(
        rdb=trunk_rdb,
        s3=trunk_s3,
        tenant_registry=tenants,
        drawing_settings=DrawingServiceSettings(),
        chat_db_settings=None,
        wopi_settings=WopiServiceSettings(wopi_client_host="http://this.is.not.a.host.mock.somehow?"),
        api_keys=QwTrunkApiKeys(
            claude_api_key="mock",
            groq_api_key="mock",
            openai_key="mock",
            gemini_api_key="mock",
            mistral_api_key="mock",
        ),
        lf=log_factory,
    )

    token_inspector = iam.get_openid_token_inspector()

    app = QwMonoApp(
        info=QwAppInfo(
            name="qw-mono",
            version=app_version,
            commit="0" * 40,
        ),
        pfoertner=QwPfoertnerModule(
            iam=iam,
            session_service=SessionService(
                rdb=pfoertner_rdb,
                openid_client=iam.get_openid_client(),
                gateway=SessionTokenGateway(salt=123, lf=log_factory),
                policy=QwPolicy.build(token_inspector, tenants, QwPolicySettings(), lf=log_factory),
                settings=SessionServiceSettings(),
                lf=log_factory,
            ),
            wopi_auth_service=WopiAuthenticationService(
                settings=WopiAuthenticationServiceSettings(jws_oct_key="rDC09N0a8bzg9jzf"),
                lf=log_factory,
            ),
            settings=QwPfoertnerSettings(include_spec_route=True),
            lf=log_factory,
        ),
        trunk=trunk_module,
        tenant_registry=tenants,
    )

    client = QwMonoTestClient(app.build())
    client_factory = QwMonoAuthClientFactory(client, iam, iam.realm, iam)
    return QwMonoTestApp(app, client_factory)
