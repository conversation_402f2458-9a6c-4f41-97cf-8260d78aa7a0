from enum import Enum, unique

from sqlalchemy import Foreign<PERSON>ey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from qw_monodb.table.base import Base


@unique
class DrawingFileAnalysisStatus(str, Enum):
    ANALYZING = "ANALYZING"
    COMPLETED = "COMPLETED"
    FAILURE = "FAILURE"
    PENDING = "PENDING"


class DrawingFileAnalysis(Base):
    __tablename__ = "drawing_file_analysis"
    __table_args__ = (UniqueConstraint("file_resource_revision_id", "page_index", name="unique_drawing_file_analysis"),)

    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_revision_id: Mapped[int] = mapped_column(ForeignKey("file_resource_revision.id"))
    page_index: Mapped[int | None] = mapped_column(nullable=True)
    analysis_obj_id: Mapped[int | None] = mapped_column(ForeignKey("s3_object_reference.id"), nullable=True)
    analysis_status: Mapped[DrawingFileAnalysisStatus]


class DrawingFilePage(Base):
    __tablename__ = "drawing_file_page"
    __table_args__ = (UniqueConstraint("file_resource_revision_id", "page_index", name="unique_drawing_file_page"),)

    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_revision_id: Mapped[int] = mapped_column(ForeignKey("file_resource_revision.id"))
    page_index: Mapped[int]
    image_obj_id: Mapped[int] = mapped_column(ForeignKey("s3_object_reference.id"))
    thumbnail_obj_id: Mapped[int | None] = mapped_column(ForeignKey("s3_object_reference.id"), nullable=True)


class DrawingFileTile(Base):
    __tablename__ = "drawing_file_tile"
    __table_args__ = (
        UniqueConstraint(
            "file_resource_revision_id", "page_index", "dpi", "tile_x", "tile_y", name="unique_drawing_file_tile"
        ),
    )

    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_revision_id: Mapped[int] = mapped_column(ForeignKey("file_resource_revision.id"))
    page_index: Mapped[int]
    dpi: Mapped[int]
    tile_x: Mapped[int]
    tile_y: Mapped[int]
    tile_obj_id: Mapped[int] = mapped_column(ForeignKey("s3_object_reference.id"))


class DrawingFileNormalizedQuad(Base):
    __tablename__ = "drawing_file_normalized_quad"

    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_revision_id: Mapped[int] = mapped_column(ForeignKey("file_resource_revision.id"))
    page_index: Mapped[int]
    x1: Mapped[float]
    y1: Mapped[float]
    x2: Mapped[float]
    y2: Mapped[float]
    x3: Mapped[float]
    y3: Mapped[float]
    x4: Mapped[float]
    y4: Mapped[float]


class DrawingDiscussion(Base):
    __tablename__ = "drawing_discussion"

    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_id: Mapped[int] = mapped_column(ForeignKey("file_resource.id"))
    quad_id: Mapped[int] = mapped_column(ForeignKey("drawing_file_normalized_quad.id"))
    chat_id: Mapped[int] = mapped_column(ForeignKey("chat.id"))
    resolved: Mapped[bool]
