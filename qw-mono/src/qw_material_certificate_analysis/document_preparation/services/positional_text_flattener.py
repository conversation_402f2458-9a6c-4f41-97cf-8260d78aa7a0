from typing import List

from qw_log_interface import NO_LOGGER, Logger
from qw_material_certificate_analysis.document_preparation.services.google_vision.models import (
    PositionalSidecar,
    PositionalTextElement,
)


class PositionalTextFlattener:
    """Service to flatten positional text elements into linear reading order with x-coordinates."""

    def __init__(self, logger: Logger = NO_LOGGER):
        self.logger = logger

    def flatten_to_linear_text(self, positional_sidecar: PositionalSidecar) -> str:
        """
        Flatten positional text elements into linear text ordered left-to-right, top-to-bottom.

        Format:
        25: word1(45) word2(67) word3(89)
        26: word4(12) word5(78)
        27: word6(34) word7(56) word8(90)

        Where:
        - Numbers before colon are line numbers
        - Numbers in parentheses are normalized x-coordinates (0-100, 2 decimal precision)
        """
        try:
            # Group elements by page
            pages = self._group_by_page(positional_sidecar.text_elements)

            # Calculate page dimensions for normalization
            page_dimensions = self._calculate_page_dimensions(positional_sidecar.text_elements)

            flattened_lines: List[str] = []
            global_line_num = 1

            for page_num, elements in pages.items():
                if page_num > 0:  # Add page separator
                    flattened_lines.append(f"\n=== PAGE {page_num + 1} ===")

                # Group elements into lines based on y-coordinate
                lines = self._group_into_lines(elements)

                # Get page width for normalization
                page_width = page_dimensions.get(page_num, 1000)  # Default fallback

                # Process each line
                for line_elements in lines:
                    # Sort elements in line by x-coordinate (left to right)
                    sorted_elements = sorted(line_elements, key=lambda e: e.bbox.x1)

                    # Create line text with normalized x-coordinates
                    line_words = []
                    for element in sorted_elements:
                        center_x = (element.bbox.x1 + element.bbox.x2) / 2
                        # Normalize to 0-100 with 2 decimal precision
                        normalized_x = round((center_x / page_width) * 100, 2)
                        word_with_pos = f"{element.text}({normalized_x:05.2f})"
                        line_words.append(word_with_pos)

                    if line_words:  # Only add non-empty lines
                        line_text = f"{global_line_num}: {' '.join(line_words)}"
                        flattened_lines.append(line_text)
                        global_line_num += 1

            result = "\n".join(flattened_lines)
            self.logger.info(
                f"Flattened {len(positional_sidecar.text_elements)} text elements into {len(flattened_lines)} lines"
            )

            return result

        except Exception as e:
            self.logger.error(f"Error flattening positional text: {e}")
            return ""

    def _group_by_page(self, elements: List[PositionalTextElement]) -> dict[int, List[PositionalTextElement]]:
        """Group text elements by page number."""
        pages: dict[int, List[PositionalTextElement]] = {}

        for element in elements:
            page_num = element.page
            if page_num not in pages:
                pages[page_num] = []
            pages[page_num].append(element)

        return pages

    def _calculate_page_dimensions(self, elements: List[PositionalTextElement]) -> dict[int, float]:
        """Calculate page width for each page based on maximum x-coordinate."""
        page_dimensions: dict[int, float] = {}

        for element in elements:
            page_num = element.page
            max_x = element.bbox.x2

            if page_num not in page_dimensions:
                page_dimensions[page_num] = max_x
            else:
                page_dimensions[page_num] = max(page_dimensions[page_num], max_x)

        return page_dimensions

    def _group_into_lines(self, elements: List[PositionalTextElement]) -> List[List[PositionalTextElement]]:
        """Group text elements into lines based on y-coordinate proximity."""
        if not elements:
            return []

        # Sort elements by y-coordinate (top to bottom)
        sorted_elements = sorted(elements, key=lambda e: e.bbox.y1)

        lines: List[List[PositionalTextElement]] = []
        current_line: List[PositionalTextElement] = [sorted_elements[0]]
        current_y = sorted_elements[0].bbox.y1

        # Group elements that are close in y-coordinate into same line
        line_threshold = 20  # pixels - adjust if needed

        for element in sorted_elements[1:]:
            element_y = element.bbox.y1

            # If element is close to current line's y-coordinate, add to current line
            if abs(element_y - current_y) <= line_threshold:
                current_line.append(element)
            else:
                # Start new line
                if current_line:
                    lines.append(current_line)
                current_line = [element]
                current_y = element_y

        # Add the last line
        if current_line:
            lines.append(current_line)

        return lines

    @classmethod
    def from_config(cls, logger: Logger = NO_LOGGER) -> "PositionalTextFlattener":
        """Create service from configuration."""
        return cls(logger=logger)
