import tempfile
from pathlib import Path
from typing import Optional

import ocrmypdf

from qw_log_interface import NO_LOGGER, Logger


class OCRmyPDFService:
    def __init__(
        self,
        debug_mode: bool = False,
        debug_output_dir: Optional[Path] = None,
        logger: Logger = NO_LOGGER,
    ):
        self.debug_mode = debug_mode
        self.debug_output_dir = debug_output_dir
        self.logger = logger

    def process_pdf_with_ocrmypdf(self, pdf_bytes: bytes) -> bytes:
        try:
            # Create temporary files for input and output
            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as input_temp:
                input_temp.write(pdf_bytes)
                input_path = Path(input_temp.name)

            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as output_temp:
                output_path = Path(output_temp.name)

            try:
                ocrmypdf_options = {
                    "language": ["eng"],
                    "output_type": "pdf",
                    "optimize": 1,
                    "force_ocr": True,
                    "progress_bar": False,
                    "keep_temporary_files": self.debug_mode,
                }

                _ = ocrmypdf.ocr(input_file=input_path, output_file=output_path, **ocrmypdf_options)  # type: ignore

                with open(output_path, "rb") as f:
                    output_pdf_bytes = f.read()

                if self.debug_mode and self.debug_output_dir:
                    self._save_debug_pdf(output_pdf_bytes)

                return output_pdf_bytes

            finally:
                input_path.unlink(missing_ok=True)
                output_path.unlink(missing_ok=True)

        except Exception as e:
            self.logger.error(f"OCRmyPDF processing failed: {e}")
            if self.debug_mode and self.debug_output_dir:
                self._save_debug_pdf(pdf_bytes)
            return pdf_bytes

    def _save_debug_pdf(self, pdf_bytes: bytes) -> None:
        if not self.debug_output_dir:
            return
        try:
            debug_dir = Path(self.debug_output_dir)
            debug_dir.mkdir(parents=True, exist_ok=True)
            pdf_path = debug_dir / "ocrmypdf_searchable.pdf"
            with open(pdf_path, "wb") as f:
                f.write(pdf_bytes)
        except Exception as e:
            self.logger.error(f"Failed to save debug PDF: {e}")

    @classmethod
    def from_config(
        cls,
        debug_mode: bool = False,
        debug_output_dir: Optional[str] = None,
        logger: Logger = NO_LOGGER,
    ) -> "OCRmyPDFService":
        debug_path = Path(debug_output_dir) if debug_output_dir else None
        return cls(
            debug_mode=debug_mode,
            debug_output_dir=debug_path,
            logger=logger,
        )
