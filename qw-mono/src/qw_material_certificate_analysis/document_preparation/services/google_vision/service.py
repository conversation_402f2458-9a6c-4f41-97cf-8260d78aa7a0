"""Vision processing service for material certificates using dedicated Google Cloud Vision API."""
import io
from pathlib import Path
from typing import List, Optional

import fitz  # PyMuPDF
import numpy as np
import numpy.typing as npt
from PIL import Image

from qw_log_interface import NO_LOGGER, Logger
from qw_material_certificate_analysis.document_preparation.services.google_vision.api_client import (
    GoogleCloudVisionApiService,
)
from qw_material_certificate_analysis.document_preparation.services.google_vision.models import (
    DocumentText,
    PDFType,
    PositionalSidecar,
    VisionProcessingResult,
)


class GoogleVisionService:
    """Vision processing service for material certificates with dedicated Google Vision API."""

    def __init__(
        self,
        gemini_api_key: str,
        debug_mode: bool = False,
        debug_output_dir: Optional[Path] = None,
        logger: Logger = NO_LOGGER,
    ):
        """Initialize the vision service."""
        self.logger = logger
        self.debug_mode = debug_mode
        self.debug_output_dir = debug_output_dir

        # Initialize dedicated Google Vision API service
        self.vision_service = GoogleCloudVisionApiService(
            api_key=gemini_api_key, debug_mode=debug_mode, debug_output_dir=debug_output_dir, logger=logger
        )

    async def process_pdf(self, pdf_bytes: bytes) -> VisionProcessingResult:
        """Process PDF with Google Cloud Vision API."""
        self.logger.info("Starting PDF vision processing")

        # Detect PDF type
        pdf_type = self._detect_pdf_type(pdf_bytes)
        self.logger.info(f"Detected PDF type: {pdf_type}")

        # Extract pages to images
        page_images = self._extract_pages_to_images(pdf_bytes)
        total_pages = len(page_images)

        if total_pages > 10:  # Max pages check
            raise ValueError(f"PDF has {total_pages} pages, maximum allowed is 10")

        self.logger.info(f"Extracted {total_pages} pages from PDF")

        # Process each page with Google Vision API
        vision_results: List[DocumentText] = []
        processed_images: List[bytes] = []
        all_positional_elements = []

        for i, page_image in enumerate(page_images):
            self.logger.info(f"Processing page {i+1}/{total_pages} with Google Vision API")

            # Convert bytes to numpy array
            image_array = self._bytes_to_numpy(page_image)

            # Process with Google Vision API
            document_text, raw_response, page_positional_sidecar = await self.vision_service.extract_text_from_image(
                image_array, i
            )
            vision_results.append(document_text)

            # Collect positional elements from all pages
            all_positional_elements.extend(page_positional_sidecar.text_elements)

            # For processed images, use original for now
            processed_images.append(page_image)

            # Save page-specific debug files if enabled
            if self.debug_mode and self.debug_output_dir:
                self._save_page_debug_files(i, page_image, document_text)

        self.logger.info("Vision processing completed")

        # Create combined positional sidecar

        combined_positional_sidecar = PositionalSidecar(text_elements=all_positional_elements)

        return VisionProcessingResult(
            pdf_type=pdf_type,
            total_pages=total_pages,
            page_images=page_images,
            vision_results=vision_results,
            processed_images=processed_images,
            positional_sidecar=combined_positional_sidecar,
        )

    def _detect_pdf_type(self, pdf_bytes: bytes) -> PDFType:
        """Detect if PDF is native (text-based) or raster (image-based)."""
        try:
            doc = fitz.open(stream=pdf_bytes, filetype="pdf")

            # Check first page for text content
            if len(doc) > 0:
                page = doc[0]
                text = page.get_text()

                # If page has substantial text content, consider it native
                if len(text.strip()) > 50:
                    return PDFType.NATIVE

            return PDFType.RASTER

        except Exception as e:
            self.logger.warning(f"Error detecting PDF type: {e}, defaulting to RASTER")
            return PDFType.RASTER

    def _extract_pages_to_images(self, pdf_bytes: bytes) -> List[bytes]:
        """Extract PDF pages as images."""
        page_images: List[bytes] = []

        try:
            doc = fitz.open(stream=pdf_bytes, filetype="pdf")

            for page_num in range(len(doc)):
                page = doc[page_num]

                # Render page as image with high DPI for better OCR
                mat = fitz.Matrix(4.0, 4.0)  # 4x scaling for better quality
                pix = page.get_pixmap(matrix=mat)

                # Convert to PIL Image and then to bytes
                img_data = pix.tobytes("png")
                page_images.append(img_data)

            doc.close()

        except Exception as e:
            self.logger.error(f"Error extracting pages: {e}")
            raise

        return page_images

    def _bytes_to_numpy(self, image_bytes: bytes) -> npt.NDArray[np.uint8]:
        """Convert image bytes to numpy array."""
        pil_image = Image.open(io.BytesIO(image_bytes))
        return np.array(pil_image)

    def _save_page_debug_files(self, page_index: int, page_image: bytes, document_text: DocumentText) -> None:
        """Save debug files for individual pages."""
        if not self.debug_output_dir:
            return

        try:
            debug_dir = Path(self.debug_output_dir)
            debug_dir.mkdir(parents=True, exist_ok=True)

            # Save page image
            image_file = debug_dir / f"page_{page_index:02d}_image.webp"
            with open(image_file, "wb") as f:
                f.write(page_image)

            # Save extracted text
            text_file = debug_dir / f"page_{page_index:02d}_extracted_text.txt"
            with open(text_file, "w") as f:
                f.write(document_text.text)

            self.logger.info(f"Saved debug files for page {page_index}")

        except Exception as e:
            self.logger.error(f"Error saving page debug files: {e}")

    @classmethod
    def from_config(
        cls,
        gemini_api_key: str,
        debug_mode: bool = False,
        debug_output_dir: Optional[str] = None,
        logger: Logger = NO_LOGGER,
    ) -> "GoogleVisionService":
        """Create service from configuration."""
        debug_path = Path(debug_output_dir) if debug_output_dir else None

        return cls(gemini_api_key=gemini_api_key, debug_mode=debug_mode, debug_output_dir=debug_path, logger=logger)
