"""Material Certificate Analysis Orchestrator - Single point of workflow control."""

import asyncio
import json
from pathlib import Path
from typing import Any, List, Optional, cast

from qw_log_interface import NO_LOGGER, Logger
from qw_material_certificate_analysis.agentic_pipeline.chemical_composition_agent import (
    create_chemical_composition_agent,
)
from qw_material_certificate_analysis.agentic_pipeline.header_details_agent import create_header_details_agent
from qw_material_certificate_analysis.agentic_pipeline.mechanical_properties_agent import (
    create_mechanical_properties_agent,
)
from qw_material_certificate_analysis.agentic_pipeline.metadata_agent import create_metadata_agent
from qw_material_certificate_analysis.agentic_pipeline.models import (
    AggregationInput,
    CertificateMetadata,
    ChemicalComposition,
    HeaderDetails,
    MaterialCertificateAnalysisResponse,
    MechanicalProperties,
    Product,
)
from qw_material_certificate_analysis.agentic_pipeline.text_structuring_agent import create_text_structuring_agent
from qw_material_certificate_analysis.document_preparation.services.google_vision.models import VisionProcessingResult
from qw_material_certificate_analysis.document_preparation.services.google_vision.service import GoogleVisionService
from qw_material_certificate_analysis.document_preparation.services.positional_text_flattener import (
    PositionalTextFlattener,
)


class MaterialCertificateOrchestrator:
    """
    Main orchestrator for material certificate analysis workflow.

    This is the single point of control that coordinates:
    - Document preparation services (Google Vision, OCRmyPDF, future Docling)
    - Agentic pipeline (PydanticAI-based extraction agents)
    - Final aggregation and response generation
    """

    def __init__(
        self,
        gemini_api_key: str,
        mistral_api_key: str,
        debug_mode: bool = False,
        logger: Logger = NO_LOGGER,
    ):
        self.logger = logger
        self.debug_mode = debug_mode
        self.gemini_api_key = gemini_api_key
        self.debug_output_dir: Optional[Path] = None

        # Create debug output directory if debug mode is enabled
        if debug_mode:
            if self.debug_output_dir is None:
                # Create debug directory inside the module
                module_dir = Path(__file__).parent
                self.debug_output_dir = module_dir / "debug_output"

            # Ensure debug directory exists
            self.debug_output_dir.mkdir(parents=True, exist_ok=True)
        else:
            self.debug_output_dir = None

        # Initialize document preparation services
        self.google_vision_service = GoogleVisionService(
            gemini_api_key=gemini_api_key, debug_mode=debug_mode, debug_output_dir=self.debug_output_dir, logger=logger
        )

        # Initialize positional text flattener
        self.positional_text_flattener = PositionalTextFlattener(logger=logger)

    async def analyze_certificate(self, pdf_bytes: bytes) -> MaterialCertificateAnalysisResponse:
        """Internal async implementation with proper agent lifecycle management."""
        # Create fresh agents for this analysis to avoid HTTP client reuse
        text_structuring_agent = create_text_structuring_agent(self.gemini_api_key)
        metadata_agent = create_metadata_agent(self.gemini_api_key)
        header_details_agent = create_header_details_agent(self.gemini_api_key)
        chemical_composition_agent = create_chemical_composition_agent(self.gemini_api_key)
        mechanical_properties_agent = create_mechanical_properties_agent(self.gemini_api_key)

        try:
            # Phase 1: Document preparation - Mistral AI processing
            google_vision_result = await self.google_vision_service.process_pdf(pdf_bytes)

            # Combine all extracted text for agent processing
            full_text = self._combine_vision_text(google_vision_result)

            # Generate flattened positional text for table structure analysis
            # Format: "25: word1(45.67) word2(67.89)" where numbers in parentheses are normalized x-coordinates
            flattened_positional_text = self.positional_text_flattener.flatten_to_linear_text(
                google_vision_result.positional_sidecar
            )

            # Save debug files if enabled
            if self.debug_mode and self.debug_output_dir:
                self._save_combined_text(full_text)
                self._save_flattened_positional_text(flattened_positional_text)

            # Phase 1.5: Text structuring - Organize text with positional awareness into logical sections
            structuring_result = await text_structuring_agent.run(flattened_positional_text)
            structured_text = structuring_result.output.structured_content

            # Save structured text debug file if enabled
            if self.debug_mode and self.debug_output_dir:
                self._save_structured_text(structured_text)

            # Phase 2: Agentic pipeline - Metadata collection (conversational with chat history)
            metadata_result = await metadata_agent.run(structured_text)
            metadata = metadata_result.output

            # Phase 3: Agentic pipeline - Parallel extraction with foreign key design
            # Run extraction agents in parallel, each receiving metadata context
            header_task = header_details_agent.run(structured_text, deps=metadata)
            chemical_task = chemical_composition_agent.run(structured_text, deps=metadata)
            mechanical_task = mechanical_properties_agent.run(structured_text, deps=metadata)

            # Wait for all extraction agents to complete
            header_result, chemical_result, mechanical_result = await asyncio.gather(
                header_task, chemical_task, mechanical_task
            )

            # Phase 4: Final aggregation and response generation
            return await self._complete_analysis(metadata, header_result, chemical_result, mechanical_result)

        finally:
            # CRITICAL: Explicitly close all agent HTTP clients before event loop ends
            agents = [
                text_structuring_agent,
                metadata_agent,
                header_details_agent,
                chemical_composition_agent,
                mechanical_properties_agent,
            ]

            # Close all HTTP clients with error handling
            close_tasks = []
            for agent in agents:
                try:
                    if hasattr(agent, "model") and hasattr(agent.model, "client"):
                        if hasattr(agent.model.client, "aclose"):
                            close_tasks.append(agent.model.client.aclose())
                except Exception as e:
                    self.logger.warning(f"Error accessing agent client for cleanup: {e}")

            if close_tasks:
                await asyncio.gather(*close_tasks, return_exceptions=True)

            # Brief cleanup delay
            await asyncio.sleep(0.01)

    async def _complete_analysis(
        self, metadata: CertificateMetadata, header_result: Any, chemical_result: Any, mechanical_result: Any
    ) -> MaterialCertificateAnalysisResponse:
        """Complete the analysis by aggregating results using foreign key matching."""
        # Extract outputs from agent results
        header_details = cast(List[HeaderDetails], header_result.output or [])
        chemical_compositions = cast(List[ChemicalComposition], chemical_result.output or [])
        mechanical_properties = cast(List[MechanicalProperties], mechanical_result.output or [])

        # Prepare aggregation input with foreign key design
        aggregation_input = AggregationInput(
            metadata=metadata,
            header_details=header_details,
            chemical_compositions=chemical_compositions,
            mechanical_properties=mechanical_properties,
        )

        # Save intermediate results if debug mode
        if self.debug_mode and self.debug_output_dir:
            self._save_intermediate_results(aggregation_input)

        # Simple foreign key aggregation (no AI needed - just data transformation)
        # Create products by matching foreign keys
        products: List[Product] = []
        for header in aggregation_input.header_details:
            # Find matching chemical composition by batch_number and heat_number
            chemical_comp: Optional[ChemicalComposition] = None
            for chem in aggregation_input.chemical_compositions:
                if chem.batch_number == header.batch_number and chem.heat_number == header.heat_number:
                    chemical_comp = chem
                    break

            # Find matching mechanical properties by batch_number and heat_number
            mechanical_props: Optional[MechanicalProperties] = None
            for mech in aggregation_input.mechanical_properties:
                if mech.batch_number == header.batch_number and mech.heat_number == header.heat_number:
                    mechanical_props = mech
                    break

            # Create product with foreign key matches
            product = Product(
                batch_number=header.batch_number,
                heat_number=header.heat_number,
                header_details=header,
                chemical_composition=chemical_comp,
                mechanical_properties=mechanical_props,
            )
            products.append(product)

        # Create final response
        final_result = MaterialCertificateAnalysisResponse(metadata=aggregation_input.metadata, products=products)

        return final_result

    def _combine_vision_text(self, vision_result: VisionProcessingResult) -> str:
        """Combine vision processing results into single text for agent processing."""
        combined_text: List[str] = []

        for i, document_text in enumerate(vision_result.vision_results):
            page_header = f"\n=== PAGE {i+1} ===\n"
            combined_text.append(page_header)
            combined_text.append(document_text.text)
            combined_text.append("\n")

        return "".join(combined_text)

    def _save_combined_text(self, full_text: str) -> None:
        """Save combined text for debugging purposes."""
        if not self.debug_output_dir:
            return

        try:
            debug_dir = Path(self.debug_output_dir)
            debug_dir.mkdir(parents=True, exist_ok=True)

            text_file = debug_dir / "combined_text_for_agents.txt"
            with open(text_file, "w", encoding="utf-8") as f:
                f.write(full_text)

        except Exception as e:
            self.logger.error(f"Error saving combined text: {e}")

    def _save_structured_text(self, structured_text: str) -> None:
        """Save structured text for debugging purposes."""
        if not self.debug_output_dir:
            return

        try:
            debug_dir = Path(self.debug_output_dir)
            debug_dir.mkdir(parents=True, exist_ok=True)

            text_file = debug_dir / "structured_text_output.txt"
            with open(text_file, "w", encoding="utf-8") as f:
                f.write(structured_text)

        except Exception as e:
            self.logger.error(f"Error saving structured text: {e}")

    def _save_flattened_positional_text(self, flattened_text: str) -> None:
        """Save flattened positional text for debugging purposes."""
        if not self.debug_output_dir:
            return

        try:
            debug_dir = Path(self.debug_output_dir)
            debug_dir.mkdir(parents=True, exist_ok=True)

            flattened_file = debug_dir / "flattened_positional_text.txt"
            with open(flattened_file, "w", encoding="utf-8") as f:
                f.write(flattened_text)

        except Exception as e:
            self.logger.error(f"Error saving flattened positional text: {e}")

    def _save_intermediate_results(self, aggregation_input: AggregationInput) -> None:
        """Save intermediate results for debugging purposes."""
        if not self.debug_output_dir:
            return

        try:
            debug_dir = Path(self.debug_output_dir)
            debug_dir.mkdir(parents=True, exist_ok=True)

            # Save metadata
            metadata_file = debug_dir / "agent_result_metadata.json"
            with open(metadata_file, "w", encoding="utf-8") as f:
                json.dump(aggregation_input.metadata.model_dump(), f, indent=2, default=str)

            # Save header details
            header_file = debug_dir / "agent_result_header_details.json"
            with open(header_file, "w", encoding="utf-8") as f:
                json.dump([h.model_dump() for h in aggregation_input.header_details], f, indent=2, default=str)

            # Save chemical compositions
            chemical_file = debug_dir / "agent_result_chemical_compositions.json"
            with open(chemical_file, "w", encoding="utf-8") as f:
                json.dump([c.model_dump() for c in aggregation_input.chemical_compositions], f, indent=2, default=str)

            # Save mechanical properties
            mechanical_file = debug_dir / "agent_result_mechanical_properties.json"
            with open(mechanical_file, "w", encoding="utf-8") as f:
                json.dump([m.model_dump() for m in aggregation_input.mechanical_properties], f, indent=2, default=str)

            # Save complete aggregation input
            aggregation_file = debug_dir / "aggregation_input.json"
            with open(aggregation_file, "w", encoding="utf-8") as f:
                json.dump(aggregation_input.model_dump(), f, indent=2, default=str)

        except Exception as e:
            self.logger.error(f"Error saving intermediate results: {e}")

    @classmethod
    def from_config(
        cls,
        gemini_api_key: str,
        mistral_api_key: str,
        save_debug_files: bool = False,
        logger: Logger = NO_LOGGER,
    ) -> "MaterialCertificateOrchestrator":
        """Create orchestrator from configuration."""
        return cls(
            gemini_api_key=gemini_api_key, mistral_api_key=mistral_api_key, debug_mode=save_debug_files, logger=logger
        )
