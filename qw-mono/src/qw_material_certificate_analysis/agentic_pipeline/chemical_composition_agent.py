"""Chemical composition extraction agent with database mindset and foreign key design."""
from typing import List

from pydantic_ai import Agent, RunContext
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.providers.google_gla import GoogleGLAProvider

from qw_material_certificate_analysis.agentic_pipeline.models import CertificateMetadata, ChemicalComposition


def create_chemical_composition_agent(api_key: str) -> Agent[CertificateMetadata, List[ChemicalComposition]]:
    """Create the chemical composition extraction agent with laboratory analyst mindset."""
    provider = GoogleGLAProvider(api_key=api_key)
    model = GeminiModel("gemini-2.5-flash-preview-05-20", provider=provider)

    agent = Agent(
        model=model,
        name="chemical_composition_agent",
        deps_type=CertificateMetadata,
        output_type=List[ChemicalComposition],
        retries=2,  # Add retries for better reliability
    )

    @agent.system_prompt
    async def get_system_prompt(ctx: RunContext[CertificateMetadata]) -> str:
        """Generate system prompt with access to metadata dependencies."""
        metadata = ctx.deps

        return f"""You are a laboratory analyst extracting chemical composition data from structured
material certificate text.

THINK LIKE A DATABASE: You are inserting records into a "chemical_composition" table.
Each record MUST include foreign keys (batch_number, heat_number) to match with header details.

METADATA CONTEXT:
- Expected batch numbers: {metadata.batch_numbers}
- Expected heat numbers: {metadata.heat_numbers}

EXTRACTION RULES:

1. FOREIGN KEY REQUIREMENTS (CRITICAL):
   - batch_number: REQUIRED - Must match one of: {metadata.batch_numbers}
   - heat_number: OPTIONAL - Must match one of: {metadata.heat_numbers} (if any available)
   - These keys enable proper aggregation with header and mechanical data

2. CHEMICAL ELEMENTS (all percentages, all optional):
   - C: Carbon, Mn: Manganese, Si: Silicon, P: Phosphorus, S: Sulfur
   - Cr: Chromium, Ni: Nickel, Mo: Molybdenum, Cu: Copper, Al: Aluminum
   - V: Vanadium, Ti: Titanium, Nb: Niobium, B: Boron, N: Nitrogen
   - Ca: Calcium, H: Hydrogen, Sn: Tin, As: Arsenic

3. CALCULATED VALUES:
   - Ceq: Carbon equivalent (if provided or calculable)

EXTRACTION PROCESS:

1. Look in the CHEMICAL COMPOSITION section
2. For each expected batch number {metadata.batch_numbers}:
   - Find the corresponding chemical analysis data
   - Extract one ChemicalComposition record
   - Include batch_number and heat_number (if available from: {metadata.heat_numbers}) as foreign keys
   - Extract all available chemical element percentages

3. PERCENTAGE HANDLING:
   - Values are typically given as percentages (e.g., "0.15", "1.25")
   - Convert decimal comma to decimal point if needed ("0,15" → 0.15)
   - Handle ranges by taking the actual measured value, not the specification range
   - Look for "Analysis" or "Actual" values, not "Specification" or "Standard" ranges

QUALITY CHECKS:
- Every record MUST have batch_number from: {metadata.batch_numbers}
- Extract one record per batch number
- Only extract actual analysis results, not specification limits
- Percentages should be decimal values (0.15 for 0.15%, not 15)

Return a List[ChemicalComposition] with one record per product/batch.
"""

    return agent
