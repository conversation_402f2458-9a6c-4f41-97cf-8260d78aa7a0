"""Metadata collection agent with conversational context building."""
from pydantic_ai import Agent
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.providers.google_gla import GoogleGLAProvider

from qw_material_certificate_analysis.agentic_pipeline.models import CertificateMetadata


def create_metadata_agent(api_key: str) -> Agent[None, CertificateMetadata]:
    """Create the metadata collection agent for structured text analysis."""
    provider = GoogleGLAProvider(api_key=api_key)
    model = GeminiModel("gemini-2.5-flash-preview-05-20", provider=provider)
    return Agent(
        model=model,
        name="metadata_agent",
        output_type=CertificateMetadata,
        retries=2,  # Add retries for better reliability
        instructions="""
Extract metadata from structured certificate text with careful identifier classification.

1. PRODUCT TYPE: Identify based on keywords and dimensions
   - "plate", "sheet" (flat), "bar", "rod" (long), "tube", "pipe" (hollow)


2. BATCH NUMBERS: Individual product identifiers
   - Keywords: "batch", "plate", NOT "piece", "position" NOT any cardinal number
   - Batch numbers should be alike (e.g., all "12352921", "12445692" in rare cases "42011-10", "42011-20")

3. HEAT NUMBERS: Production batch identifiers
   - Keywords: "heat", "smelt", "cast", "melt", "charge"
   - Heat numbers should be alike (e.g., all "H4569", "H4573" or all "Heat 0001", "Heat 0002")

QUALITY CHECK:
   - Ensure batch numbers are alike and heat numbers are alike. Don't mix types.
   - Group semantically similar identifiers together
   - Avoid mixing identifier types (e.g., don't classify "12352921" and "H4569" under the same category)
   - An identifier cannot simultaneously be both a heat and a batch number; it must belong to only one category.

Return CertificateMetadata with product_type, batch_numbers, and heat_numbers.
""",
    )
