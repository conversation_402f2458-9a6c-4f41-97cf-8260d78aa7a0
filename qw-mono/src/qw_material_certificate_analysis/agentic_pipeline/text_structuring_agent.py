"""Text structuring agent for organizing raw vision text into logical sections."""
from pydantic_ai import Agent
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.providers.google_gla import GoogleGLAProvider

from qw_material_certificate_analysis.agentic_pipeline.models import StructuredText


def create_text_structuring_agent(api_key: str) -> Agent[None, StructuredText]:
    """Create the text structuring agent for organizing raw text."""
    provider = GoogleGLAProvider(api_key=api_key)
    model = GeminiModel("gemini-2.5-flash-preview-05-20", provider=provider)
    return Agent(
        model=model,
        name="text_structuring_agent",
        output_type=StructuredText,
        retries=2,  # Add retries for better reliability
        instructions="""
Structure this text into logical sections with improved table formatting and language consistency.

INPUT FORMAT:
The text contains positional information in this format:
25: word1(45.67) word2(67.89) word3(89.12)
26: word4(12.34) word5(78.90)

Where:
- Numbers before colon: Line numbers
- Numbers in parentheses: Normalized x-coordinates (0-100, 2 decimal precision)

CORE TASKS:

1. LANGUAGE PREFERENCE:
   - If multiple languages present, prefer English over German over others
   - Use only one language per section (no duplicates)

2. TABLE FORMATTING WITH POSITIONAL AWARENESS:
   - Convert tables to TSV format using ```tsv blocks
   - Use the x-coordinates to determine correct column order (left-to-right by x-values)
   - When parsing table headers, sort them by x-coordinates: Heat(15.23) C(25.67) Mn(35.89)
   - Ensure data rows follow the same column ordering as headers
   - Preserve all data values exactly
   - Use tabs between columns, newlines between rows
   - Outside of the tsv block mention which page(s) is the table taken from

3. SECTION ORGANIZATION:
   - Header/Company Information
   - Product Specifications
   - Chemical Composition
   - Mechanical Properties (Includes Tensile and Impact Tests)
   - Certifications/Standards

4. QUALITY CHECKS:
   - Group semantically similar identifiers together
   - Batch numbers should be alike (e.g., all "12352921", "12445692" in rare cases "42011-10", "42011-20")
   - Heat numbers should be alike (e.g., all "H4569", "H4573" or all "Heat 0001", "Heat 0002")
   - Avoid mixing identifier types (e.g., don't classify "B123" and "H4569" under the same category)
   - An identifier cannot simultaneously be both a heat and a batch number; it must belong to only one category.

POSITIONAL TABLE PARSING EXAMPLE:
Input:
45: Heat(15.23) C(25.67) Mn(35.89) Si(45.12)
46: 304275(15.20) 0.16(25.70) 1.41(35.85) 0.213(45.15)
47: 393008(15.18) 0.16(25.72) 1.43(35.87) 0.222(45.13)

Analysis: Headers sorted by x-coordinate: Heat(15.23) < C(25.67) < Mn(35.89) < Si(45.12)
Data rows follow same x-coordinate pattern, confirming column alignment.

Output:
```tsv
Heat	C	Mn	Si
304275	0.16	1.41	0.**********	0.16	1.43	0.222
```

EXAMPLE OUTPUT:
=== HEADER INFORMATION ===
Company: Manufacturer Name
Address: Street, City, Country

=== PRODUCT SPECIFICATIONS ===
```tsv
Batch	Grade	Thickness	Width	Length	Weight
12352921	S355J2+N	15.0	2450	12000	3456
12445692	S355J2+N	15.0	2450	12000	3456
```

=== CHEMICAL COMPOSITION ===
```tsv
Heat	C	Mn	Si	P	S
304275	0.16	1.41	0.213	0.010	0.**********	0.16	1.43	0.222	0.012	0.020
```

Return structured text with clear sections and properly ordered TSV tables based on positional coordinates.
""",
    )
