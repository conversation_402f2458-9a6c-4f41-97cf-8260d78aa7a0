"""Mechanical properties extraction agent with database mindset and foreign key design."""
from typing import List

from pydantic_ai import Agent, RunContext
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.providers.google_gla import GoogleGLAProvider

from qw_material_certificate_analysis.agentic_pipeline.models import CertificateMetadata, MechanicalProperties


def create_mechanical_properties_agent(api_key: str) -> Agent[CertificateMetadata, List[MechanicalProperties]]:
    """Create the mechanical properties extraction agent with testing engineer mindset."""
    provider = GoogleGLAProvider(api_key=api_key)
    model = GeminiModel("gemini-2.5-flash-preview-05-20", provider=provider)

    agent = Agent(
        model=model,
        name="mechanical_properties_agent",
        deps_type=CertificateMetadata,
        output_type=List[MechanicalProperties],
        retries=2,  # Add retries for better reliability
    )

    @agent.system_prompt
    async def get_system_prompt(ctx: RunContext[CertificateMetadata]) -> str:
        """Generate system prompt with access to metadata dependencies."""
        metadata = ctx.deps

        return f"""You are a testing engineer extracting mechanical properties data
from structured material certificate text.

THINK LIKE A DATABASE: You are inserting records into a "mechanical_properties" table.
Each record MUST include foreign keys (batch_number, heat_number) to match with header and chemical data.

METADATA CONTEXT:
- Expected batch numbers: {metadata.batch_numbers}
- Expected heat numbers: {metadata.heat_numbers}

EXTRACTION RULES:

1. FOREIGN KEY REQUIREMENTS (CRITICAL):
   - at least one of batch_number or heat_number is required
   - batch_number: Must match one of: {metadata.batch_numbers} (exact match except when batch number is a
   combined key, in which case a core number match (e.g., 176159-10 matching 176159) is also acceptable.
   - heat_number: Must match one of: {metadata.heat_numbers} (exact match)
   - These keys enable proper aggregation across all data tables

2. TENSILE TEST DATA (tensile_test field):
   - test_direction: L (Longitudinal) or T (Transverse) - most common is T
   - test_temperature_celsius: Test temperature (commonly 20°C)
   - tensile_strength_mpa: Rm - Ultimate tensile strength in MPa
   - yield_strength_mpa: ReH - Yield strength in MPa
   - elongation_percent: A% - Elongation at break in percentage

3. IMPACT TEST DATA (impact_tests field - LIST):
   - Can have multiple impact tests at different temperatures
   - Each impact test contains:
     * test_direction: L (Longitudinal) or T (Transverse) - most common is L
     * test_temperature_celsius: Impact test temperature (e.g., 20, -20, -40, -60°C)
     * impact_energy_1_joules: First Charpy V-notch impact energy in Joules
     * impact_energy_2_joules: Second impact energy value
     * impact_energy_3_joules: Third impact energy value
     * impact_energy_average_joules: Average (calculate if not provided)

EXTRACTION PROCESS:

1. Look in the MECHANICAL PROPERTIES section
2. For each expected batch number {metadata.batch_numbers}:
   - Find corresponding test results by matching batch numbers OR sample IDs that relate to the batch
   - Extract one MechanicalProperties record with foreign keys (include
   heat_number from: {metadata.heat_numbers} if available)
   - Create TensileTest object if tensile data is available
   - Create list of ImpactTest objects (can be multiple tests at different temperatures)

3. TENSILE TEST EXTRACTION:
   - Look for tensile/yield strength, elongation data
   - Create TensileTest object with test conditions and results
   - Direction usually T (Transverse), temperature commonly 20°C

4. IMPACT TEST EXTRACTION:
   - Look for Charpy V-notch impact test data
   - Can have multiple tests at different temperatures (-20°C, -40°C, etc.)
   - Create separate ImpactTest object for each temperature
   - Direction usually L (Longitudinal)
   - Each test typically has 3 individual values plus average

QUALITY CHECKS:
- Every record MUST have batch_number from: {metadata.batch_numbers}
- Extract one record per batch number
- Strength values should be reasonable (typically 200-800 MPa for structural steel)
- Elongation should be percentage (typically 15-35% for steel)
- Impact energy should be positive Joules (typically 20-200J)
- Calculate impact average if individual values provided but average missing

Return a List[MechanicalProperties] with one record per product/batch.
"""

    return agent
