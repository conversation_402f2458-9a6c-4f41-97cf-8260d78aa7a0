from pydantic import BaseModel

from qw_basic_rdb.builder.base import RelationalDatabaseConfig
from qw_basic_s3.builder import S3StorageConfig
from qw_trunk.service.chat_db import ChatDatabaseServiceSettings
from qw_trunk.service.drawing.drawing import DrawingServiceSettings
from qw_trunk.service.wopi import WopiServiceSettings


class QwTrunkApiKeys(BaseModel):
    claude_api_key: str
    groq_api_key: str
    openai_key: str
    gemini_api_key: str
    mistral_api_key: str


class QwTrunkBaseConfig(BaseModel):
    drawing_settings: DrawingServiceSettings
    chat_db_settings: ChatDatabaseServiceSettings
    wopi_settings: WopiServiceSettings
    api_keys: QwTrunkApiKeys


class QwTrunkConfig(QwTrunkBaseConfig):
    db: RelationalDatabaseConfig
    s3: S3StorageConfig
