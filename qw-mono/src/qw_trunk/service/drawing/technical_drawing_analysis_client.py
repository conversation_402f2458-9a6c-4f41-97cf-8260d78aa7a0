"""Client for interacting with technical drawing analysis tasks."""
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import select
from sqlalchemy.orm import Session as RdbSession

from qw_basic_rdb.common import TransactionStrategy, begin_or_use_session
from qw_basic_rdb.interface import RelationalDatabase
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_monodb.table.trunk.drawing import DrawingFileAnalysis, DrawingFileAnalysisStatus


class TechnicalDrawingAnalysisClient:
    """Client for interacting with technical drawing analysis tasks."""

    def __init__(
        self,
        rdb: RelationalDatabase,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.rdb = rdb
        self.logger = lf.get_logger(__name__)

    def start_analysis(
        self,
        file_resource_revision_id: int,
        bucket: str,
        page_index: int,
        tile_infos: List[Dict[str, Any]],
        session: RdbSession,
    ) -> Dict[str, Any]:
        """
        Create analysis record and return task parameters.
        Task queueing is handled by the caller after transaction commits.

        Args:
            file_resource_revision_id: ID of the file revision to analyze
            bucket: S3 bucket containing tiles
            page_index: Index of the page to analyze
            tile_infos: List of dictionaries with tile information
            session: Database session (required - must be within a transaction)

        Returns:
            Dictionary with task parameters for later queueing
        """
        # Check if analysis already exists for this specific page
        existing = session.execute(
            select(DrawingFileAnalysis).where(
                (DrawingFileAnalysis.file_resource_revision_id == file_resource_revision_id)
                & (DrawingFileAnalysis.page_index == page_index)
            )
        ).scalar_one_or_none()

        if existing:
            # If analysis exists but failed, we can retry
            if existing.analysis_status == DrawingFileAnalysisStatus.FAILURE:
                existing.analysis_status = DrawingFileAnalysisStatus.PENDING
                existing.analysis_obj_id = None
                session.flush([existing])
                analysis_id = existing.id
            else:
                # Analysis already exists and is not in failed state
                analysis_id = existing.id
        else:
            # Create new analysis record for this page
            analysis = DrawingFileAnalysis(
                file_resource_revision_id=file_resource_revision_id,
                page_index=page_index,
                analysis_obj_id=None,
                analysis_status=DrawingFileAnalysisStatus.PENDING,
            )
            session.add(analysis)
            session.flush([analysis])
            analysis_id = analysis.id

        # Return task parameters instead of queueing immediately
        return {
            "analysis_id": analysis_id,
            "task_name": "qw_worker.tasks.ocr.process_technical_drawing_analysis_v2",
            "kwargs": {
                "bucket": bucket,
                "tile_infos": tile_infos,
                "file_resource_revision_id": file_resource_revision_id,
                "analysis_record_id": analysis_id,
                "page_index": page_index,
            },
        }

    def get_analysis_data(
        self,
        file_resource_revision_id: int,
        session: Optional[RdbSession] = None,
    ) -> Dict[int, Tuple[Optional[int], DrawingFileAnalysisStatus]]:
        """
        Get analysis data for all pages of a file revision.

        Args:
            file_resource_revision_id: ID of the file revision
            session: Optional database session

        Returns:
            Dictionary mapping page indices to tuples of (analysis_obj_id, analysis_status)
        """
        with begin_or_use_session(self.rdb, session, strategy=TransactionStrategy.REUSE_OR_NEW) as s:
            analyses = (
                s.execute(
                    select(DrawingFileAnalysis).where(
                        DrawingFileAnalysis.file_resource_revision_id == file_resource_revision_id
                    )
                )
                .scalars()
                .all()
            )

            result: Dict[int, Tuple[Optional[int], DrawingFileAnalysisStatus]] = {}
            for analysis in analyses:
                if analysis.page_index is not None:  # Skip records with NULL page_index
                    result[analysis.page_index] = (analysis.analysis_obj_id, analysis.analysis_status)
            return result
