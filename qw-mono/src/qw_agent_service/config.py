"""Configuration for FastAPI Agent Service."""
from pydantic import BaseModel

from qw_basic_rdb.builder.base import RelationalDatabaseConfig
from qw_basic_s3.builder import S3StorageConfig
from qw_log.factory import QwLogConfig
from qw_tenant_config.registry import QwTenantsConfig


class QwAgentServiceApiKeys(BaseModel):
    """API keys for the agent service."""

    openai_key: str
    claude_api_key: str
    groq_api_key: str
    gemini_api_key: str
    mistral_api_key: str


class QwAgentServiceBaseConfig(BaseModel):
    """Base configuration for the agent service."""

    runtime_service_url: str
    mcp_server_url: str
    api_keys: QwAgentServiceApiKeys


class QwAgentServiceConfig(QwAgentServiceBaseConfig):
    """Agent service configuration.

    This class combines all the configuration needed for the agent service.
    """

    s3: S3StorageConfig
    db: RelationalDatabaseConfig
    tenants: QwTenantsConfig


class QwAgentServiceMonoConfig(BaseModel):
    """Configuration for the agent service in the mono app.

    This class is used to load the configuration from app.yaml.
    """

    mono_agent_service: QwAgentServiceBaseConfig
    mono_s3: S3StorageConfig
    mono_db: RelationalDatabaseConfig
    tenants: QwTenantsConfig
    logging: QwLogConfig

    @property
    def agent_service(self) -> QwAgentServiceConfig:
        """Get the agent service configuration.

        Returns:
            The agent service configuration
        """
        return QwAgentServiceConfig(
            runtime_service_url=self.mono_agent_service.runtime_service_url,
            mcp_server_url=self.mono_agent_service.mcp_server_url,
            api_keys=self.mono_agent_service.api_keys,
            s3=self.mono_s3,
            db=self.mono_db,
            tenants=self.tenants,
        )
