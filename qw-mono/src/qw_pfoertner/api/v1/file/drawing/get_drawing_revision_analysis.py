import json
from typing import Dict, <PERSON>, Optional, <PERSON><PERSON>

import falcon

from qw_drawing_toolkit_ocr.post_processing.models import (
    DrawingAnalysisResult,
    NormalizedPMIBlockAnalysisResult,
    PMIMetadata,
)
from qw_falcon_openapi.controller import OpenApiPathController
from qw_falcon_openapi.model import OpenApiBaseModel
from qw_falcon_openapi.operation import NoParams, OpenApiOpOutput, OpenApiOpParams
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_monodb.table.trunk.drawing import DrawingFileAnalysisStatus
from qw_pfoertner.api.auth import AuthContext, AuthOpenApiOpJsonOut
from qw_pfoertner.api.v1.file.policy.file_resource_revision import get_file_revision_and_eval_policy
from qw_pfoertner.service.session import SessionService
from qw_trunk.service.drawing.technical_drawing_analysis_client import TechnicalDrawingAnalysisClient
from qw_trunk.service.resource.file import FileResourceService
from qw_trunk.service.resource.s3_object import S3ObjectService


class FileResourceRevisionIdParam(OpenApiBaseModel):
    file_resource_revision_id: int


class PageIndexQueryParam(OpenApiBaseModel):
    page_index: int | None = None


Params = OpenApiOpParams[FileResourceRevisionIdParam, PageIndexQueryParam, NoParams]


class TechnicalDrawingAnalysisResult(OpenApiBaseModel):
    """Result model for technical drawing analysis with per-page metadata support."""

    results: Dict[str, List[NormalizedPMIBlockAnalysisResult]] = {}
    metadata: Dict[str, Optional[PMIMetadata]] = {}


class TechnicalDrawingAnalysisResponseWithStatus(OpenApiBaseModel):
    """Response model for technical drawing analysis with status information."""

    status: str | None
    data: TechnicalDrawingAnalysisResult | None
    message: str | None


class GetDrawingRevisionAnalysis(
    AuthOpenApiOpJsonOut[
        TechnicalDrawingAnalysisResponseWithStatus, FileResourceRevisionIdParam, PageIndexQueryParam, NoParams
    ]
):
    """Get technical drawing analysis for a drawing revision."""

    def __init__(
        self,
        session_service: SessionService,
        file_service: FileResourceService,
        object_service: S3ObjectService,
        analysis_client: TechnicalDrawingAnalysisClient,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        super().__init__(
            TechnicalDrawingAnalysisResponseWithStatus,
            FileResourceRevisionIdParam,
            PageIndexQueryParam,
            NoParams,
            session_service,
            lf.get_logger(__name__),
        )
        self.file_service = file_service
        self.object_service = object_service
        self.analysis_client = analysis_client

    def _create_status_response(
        self,
        analysis_data: Dict[int, Tuple[Optional[int], DrawingFileAnalysisStatus]],
        page_index: Optional[int] = None,
    ) -> TechnicalDrawingAnalysisResponseWithStatus:
        """Create a status response for incomplete analysis."""
        total_pages = len(analysis_data)
        completed_count = sum(
            1 for obj_id, status in analysis_data.values() if obj_id and status == DrawingFileAnalysisStatus.COMPLETED
        )

        # Find highest priority status
        statuses = [status for _, status in analysis_data.values()]
        if DrawingFileAnalysisStatus.FAILURE in statuses:
            overall_status = DrawingFileAnalysisStatus.FAILURE
        elif DrawingFileAnalysisStatus.ANALYZING in statuses:
            overall_status = DrawingFileAnalysisStatus.ANALYZING
        else:
            overall_status = DrawingFileAnalysisStatus.PENDING

        if page_index is not None:
            message = f"Analysis for page {page_index} is {str(overall_status).lower()}"
        else:
            message = f"Analysis {str(overall_status).lower()} ({completed_count}/{total_pages} pages completed)"

        return TechnicalDrawingAnalysisResponseWithStatus(status=overall_status.value, data=None, message=message)

    def _create_success_response(
        self, results: Dict[str, List[NormalizedPMIBlockAnalysisResult]], metadata: Dict[str, Optional[PMIMetadata]]
    ) -> TechnicalDrawingAnalysisResponseWithStatus:
        """Create a success response with analysis data."""
        analysis_data = TechnicalDrawingAnalysisResult(results=results, metadata=metadata)
        return TechnicalDrawingAnalysisResponseWithStatus(
            status=DrawingFileAnalysisStatus.COMPLETED.value,
            data=analysis_data,
            message="Analysis completed successfully",
        )

    def _load_analysis_from_s3(self, obj_id: int) -> DrawingAnalysisResult:
        """Load and parse analysis data from S3."""
        try:
            obj = self.object_service.get_object_stream(obj_id)
            page_data = json.load(obj)
            return DrawingAnalysisResult(**page_data)
        except Exception as e:
            self.logger.warning(f"Failed parsing analysis data: {str(e)}. The drawing analysis is outdated.")
            return DrawingAnalysisResult(results={}, metadata=None)

    def on_request_after_auth(
        self, auth_ctx: AuthContext, params: Params
    ) -> OpenApiOpOutput[TechnicalDrawingAnalysisResponseWithStatus]:
        file_resource_revision_id = params.path.file_resource_revision_id
        page_index = params.query.page_index if params.query else None

        # Check permissions and get file revision
        get_file_revision_and_eval_policy(
            file_service=self.file_service,
            file_resource_revision_id=file_resource_revision_id,
            auth_ctx=auth_ctx,
            ignore_deleted=False,
        )

        # Get all analysis data - treat everything as multi-page
        all_analysis_data = self.analysis_client.get_analysis_data(file_resource_revision_id)

        if not all_analysis_data:
            raise falcon.HTTPNotFound(description="No technical drawing analysis found for this drawing revision.")

        # Filter to requested page if specified
        if page_index is not None:
            if page_index not in all_analysis_data:
                raise falcon.HTTPNotFound(
                    description=f"No technical drawing analysis found for page {page_index} of this drawing revision."
                )
            analysis_data = {page_index: all_analysis_data[page_index]}
        else:
            analysis_data = all_analysis_data

        # Check completion status
        completed_pages = [
            page
            for page, (obj_id, status) in analysis_data.items()
            if obj_id and status == DrawingFileAnalysisStatus.COMPLETED
        ]

        if len(completed_pages) != len(analysis_data):
            # Not all pages complete - return status
            return OpenApiOpOutput(self._create_status_response(analysis_data, page_index))

        # All pages complete - load and combine results with per-page metadata
        combined_results: Dict[str, List[NormalizedPMIBlockAnalysisResult]] = {}
        combined_metadata: Dict[str, Optional[PMIMetadata]] = {}

        for page_idx in sorted(completed_pages):
            obj_id, _ = analysis_data[page_idx]
            assert obj_id is not None  # Guaranteed for completed pages
            page_result = self._load_analysis_from_s3(obj_id)

            # Always add page data with page prefix
            for _, items in page_result.results.items():
                key = f"page_{page_idx}_pmis"
                combined_results[key] = items

            # Add metadata per page
            combined_metadata[f"page_{page_idx}_metadata"] = page_result.metadata

        return OpenApiOpOutput(self._create_success_response(combined_results, combined_metadata))


class GetDrawingRevisionAnalysisController(OpenApiPathController):
    def __init__(
        self,
        session_service: SessionService,
        file_service: FileResourceService,
        object_service: S3ObjectService,
        analysis_client: TechnicalDrawingAnalysisClient,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        logger = lf.get_logger(__name__)
        super().__init__(
            route="/api/v1/file-resource/drawing/revision/{fileResourceRevisionId}/analysis",
            get=GetDrawingRevisionAnalysis(
                session_service=session_service,
                file_service=file_service,
                object_service=object_service,
                analysis_client=analysis_client,
                lf=lf,
            ),
            logger=logger,
        )
