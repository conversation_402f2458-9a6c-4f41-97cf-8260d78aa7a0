import json
from typing import Optional

import falcon

from qw_falcon_openapi.controller import <PERSON>ApiPathController
from qw_falcon_openapi.model import OpenApiBaseModel
from qw_falcon_openapi.operation import NoParams, OpenApiOpOutput, OpenApiOpParams
from qw_log_interface import NO_LOG_FACTORY, LogFactory, Logger
from qw_material_certificate_analysis.agentic_pipeline.models import MaterialCertificateAnalysisResponse
from qw_monodb.table.trunk.material_certificate import MaterialCertificateAnalysis, MaterialCertificateAnalysisStatus
from qw_pfoertner.api.auth import AuthContext, AuthOpenApiOpJsonOut
from qw_pfoertner.api.v1.file.policy.file_resource_revision import get_file_revision_and_eval_policy
from qw_pfoertner.service.session import SessionService
from qw_trunk.service.material_certificate.material_certificate import MaterialCertificateService
from qw_trunk.service.resource.file import FileResourceService
from qw_trunk.service.resource.s3_object import S3ObjectService


class MaterialCertificateAnalysisResponseWithStatus(OpenApiBaseModel):
    status: Optional[str]
    data: Optional[MaterialCertificateAnalysisResponse]
    message: Optional[str]


class FileResourceRevisionIdParam(OpenApiBaseModel):
    file_resource_revision_id: int


Params = OpenApiOpParams[FileResourceRevisionIdParam, NoParams, NoParams]


class GetMaterialCertificateAnalysis(
    AuthOpenApiOpJsonOut[MaterialCertificateAnalysisResponseWithStatus, FileResourceRevisionIdParam, NoParams, NoParams]
):
    def __init__(
        self,
        session_service: SessionService,
        file_service: FileResourceService,
        material_cert_service: MaterialCertificateService,
        object_service: S3ObjectService,
        logger: Logger,
    ):
        super().__init__(
            MaterialCertificateAnalysisResponseWithStatus,
            FileResourceRevisionIdParam,
            NoParams,
            NoParams,
            session_service,
            logger,
        )
        self.file_service = file_service
        self.material_cert_service = material_cert_service
        self.object_service = object_service

    def on_request_after_auth(
        self, auth_ctx: AuthContext, params: Params
    ) -> OpenApiOpOutput[MaterialCertificateAnalysisResponseWithStatus]:
        file_resource_revision_id = params.path.file_resource_revision_id

        # Check permissions
        get_file_revision_and_eval_policy(
            file_service=self.file_service,
            file_resource_revision_id=file_resource_revision_id,
            auth_ctx=auth_ctx,
            ignore_deleted=False,
        )

        # Get analysis record
        analysis: MaterialCertificateAnalysis | None = (
            self.material_cert_service.db_service.find_material_certificate_analysis(file_resource_revision_id)
        )

        # Case 1: No analysis record exists
        if analysis is None:
            raise falcon.HTTPNotFound(description="No analysis found for this material certificate")

        # Case 2: Analysis exists but not completed
        if analysis.analysis_status != MaterialCertificateAnalysisStatus.COMPLETED:
            status_messages = {
                MaterialCertificateAnalysisStatus.PENDING: "Analysis is queued",
                MaterialCertificateAnalysisStatus.ANALYZING: "Analysis is being processed",
                MaterialCertificateAnalysisStatus.FAILURE: "Analysis failed",
            }
            message = status_messages.get(analysis.analysis_status, f"Analysis status: {analysis.analysis_status}")

            response = MaterialCertificateAnalysisResponseWithStatus(
                status=analysis.analysis_status.value, data=None, message=message
            )
            return OpenApiOpOutput(response)

        # Case 3: Analysis completed - load and return data
        # If analysis is COMPLETED but no obj_id, that's a system error
        if analysis.analysis_obj_id is None:
            self.logger.error(
                f"Analysis marked as COMPLETED but missing analysis_obj_id for revision {file_resource_revision_id}"
            )
            raise falcon.HTTPInternalServerError(description="Analysis data is corrupted")

        # Load analysis data from S3
        try:
            analysis_obj = self.object_service.get_object_stream(analysis.analysis_obj_id)
            data = json.load(analysis_obj)
        except Exception as e:
            self.logger.error(f"Error loading analysis data from S3: {str(e)}")
            raise falcon.HTTPInternalServerError(description="Error loading analysis data")

        # Parse analysis data
        try:
            analysis_data = MaterialCertificateAnalysisResponse(**data)
        except Exception as e:
            # Handle parsing failure gracefully - return minimal valid response
            self.logger.warning(
                f"Failed parsing material certificate analysis data: {str(e)}. "
                "The analysis is outdated or in legacy format. Re-run the analysis."
            )
            # Create minimal valid response for legacy/corrupted data
            analysis_data = MaterialCertificateAnalysisResponse(metadata=None, products=None)

        response = MaterialCertificateAnalysisResponseWithStatus(
            status=MaterialCertificateAnalysisStatus.COMPLETED.value,
            data=analysis_data,
            message="Analysis completed successfully",
        )
        return OpenApiOpOutput(response)


class GetMaterialCertificateAnalysisController(OpenApiPathController):
    def __init__(
        self,
        session_service: SessionService,
        file_service: FileResourceService,
        material_cert_service: MaterialCertificateService,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        logger = lf.get_logger(__name__)
        object_service = file_service.s3_service
        super().__init__(
            route="/api/v1/file-resource/material-certificate/revision/{fileResourceRevisionId}/analysis",
            get=GetMaterialCertificateAnalysis(
                session_service, file_service, material_cert_service, object_service, logger
            ),
            logger=logger,
        )
