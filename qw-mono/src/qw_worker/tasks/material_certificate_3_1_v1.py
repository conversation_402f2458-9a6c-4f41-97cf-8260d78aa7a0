"""Material certificate analysis worker task."""
import asyncio
import io
import time
from typing import Any, Callable, Dict

from celery import Celery, Task
from sqlalchemy import select

from qw_basic_rdb.common import TransactionStrategy, begin_or_use_session
from qw_basic_rdb.interface import RelationalDatabase
from qw_basic_s3.interface import S3Storage
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_material_certificate_analysis.orchestrator import MaterialCertificateOrchestrator
from qw_monodb.table.trunk.material_certificate import MaterialCertificateAnalysis, MaterialCertificateAnalysisStatus
from qw_monodb.table.trunk.resource import FileResource, FileResourceRevision
from qw_trunk.service.resource.s3_layout import S3BucketLayout
from qw_trunk.service.resource.s3_object import S3ObjectInput, S3ObjectInputData, S3ObjectService
from qw_worker.config import QwWorkerApi<PERSON>eys


def create_material_certificate_3_1_analysis_v1_task(
    app: Celery,
    s3_storage: S3Storage,  # noqa: ARG001
    api_keys: QwWorkerApiKeys,
    rdb: RelationalDatabase,
    s3_layout: S3BucketLayout,
    s3_service: S3ObjectService,
    lf: LogFactory = NO_LOG_FACTORY,
) -> Callable[..., Dict[str, Any]]:
    """Create the material certificate analysis task.

    Args:
        app: The Celery application
        s3_storage: S3 storage instance for accessing files
        api_keys: API keys for external services
        rdb: Relational database instance
        s3_layout: S3 bucket layout
        s3_service: S3 object service
        lf: Log factory to use for logging

    Returns:
        The analysis task function
    """

    @app.task(
        name="qw_worker.tasks.material_certificate_3_1_v1.analyze_material_certificate_agentic",
        bind=True,
        max_retries=1,
        soft_time_limit=1800,  # 30 minutes timeout
    )  # type: ignore
    def analyze_material_certificate_agentic(
        self: Task,
        bucket: str,
        file_resource_revision_id: int,
        analysis_record_id: int,
        pdf_data: bytes,
    ) -> Dict[str, Any]:
        """
        Run material certificate analysis and update database status.

        Args:
            self: Task instance (injected by Celery)
            file_resource_revision_id: ID of the file revision being analyzed
            analysis_record_id: ID of the analysis record to update
            pdf_data: PDF file data to analyze

        Returns:
            Dictionary with processing status
        """
        logger = lf.get_logger(__name__)
        start_time = time.time()
        logger.info(
            f"Started analysis for revision {file_resource_revision_id} with analysis record {analysis_record_id}"
        )

        try:
            # Check for deleted files and cancelled analysis
            with begin_or_use_session(rdb, strategy=TransactionStrategy.REUSE_OR_NEW) as session:
                # Check analysis record status first
                analysis_record = session.execute(
                    select(MaterialCertificateAnalysis).where(MaterialCertificateAnalysis.id == analysis_record_id)
                ).scalar_one_or_none()

                if not analysis_record:
                    return {"status": "skipped", "reason": "analysis_record_not_found"}

                if analysis_record.analysis_status != MaterialCertificateAnalysisStatus.PENDING:
                    return {"status": "skipped", "reason": "not_pending"}

                # Check if file revision exists and is not deleted
                file_revision = session.execute(
                    select(FileResourceRevision).where(FileResourceRevision.id == file_resource_revision_id)
                ).scalar_one_or_none()

                if not file_revision:
                    analysis_record.analysis_status = MaterialCertificateAnalysisStatus.FAILURE
                    session.flush([analysis_record])
                    return {"status": "skipped", "reason": "file_revision_not_found"}

                if file_revision.deleted:
                    analysis_record.analysis_status = MaterialCertificateAnalysisStatus.FAILURE
                    session.flush([analysis_record])
                    return {"status": "skipped", "reason": "file_revision_deleted"}

                # Update status to ANALYZING
                analysis_record.analysis_status = MaterialCertificateAnalysisStatus.ANALYZING
                session.flush([analysis_record])

            # Initialize agentic analysis service with debug mode
            agentic_service = MaterialCertificateOrchestrator(
                gemini_api_key=api_keys.gemini_api_key,
                mistral_api_key=api_keys.mistral_api_key,
                debug_mode=True,
                logger=logger,
            )

            # Run agentic analysis (following technical drawing pattern)
            logger.info("Running agentic multi-agent analysis")
            result = asyncio.run(agentic_service.analyze_certificate(pdf_data))

            # Store results and update database in a single transaction
            with begin_or_use_session(rdb, strategy=TransactionStrategy.REUSE_OR_NEW) as session:
                # Get file resource info
                file_revision = session.execute(
                    select(FileResourceRevision).where(FileResourceRevision.id == file_resource_revision_id)
                ).scalar_one()
                file_resource = session.execute(
                    select(FileResource).where(FileResource.id == file_revision.file_resource_id)
                ).scalar_one()

                # Create S3 path
                folder_layout = s3_layout.file_resource(file_resource.uuid, f"v{file_revision.revision_number}")
                path = folder_layout.path_to_material_certificate_analysis()

                # Serialize results using Pydantic's JSON serialization (handles Decimal objects)
                result_json = result.model_dump_json(indent=2)
                result_bio = io.BytesIO(result_json.encode())

                # Store in S3
                obj_input = S3ObjectInput(
                    bucket=bucket,
                    path=path,
                    data=S3ObjectInputData(
                        bio=result_bio,
                        size=result_bio.getbuffer().nbytes,
                        content_type="application/json",
                    ),
                )
                obj_id = s3_service.add_object(obj_input=obj_input, session=session)
                logger.info(f"Put object '{path}'")

                # Update analysis record in the same transaction
                analysis_record = session.execute(
                    select(MaterialCertificateAnalysis).where(MaterialCertificateAnalysis.id == analysis_record_id)
                ).scalar_one_or_none()

                if analysis_record:
                    analysis_record.analysis_obj_id = obj_id
                    analysis_record.analysis_status = MaterialCertificateAnalysisStatus.COMPLETED
                    session.flush([analysis_record])
                else:
                    logger.warning(
                        f"Analysis record {analysis_record_id} not found, results stored but status not updated"
                    )

            elapsed = time.time() - start_time
            logger.info(f"Total processing completed in {elapsed:.2f}s")

            return {
                "status": "success",
                "processing_time": elapsed,
                "file_resource_revision_id": file_resource_revision_id,
                "analysis_record_id": analysis_record_id,
                "result_size": len(result_json),
                "products_found": len(result.products) if result.products else 0,
            }

        except Exception as e:
            # Update status to FAILURE
            try:
                with begin_or_use_session(rdb, strategy=TransactionStrategy.REUSE_OR_NEW) as session:
                    analysis_record = session.execute(
                        select(MaterialCertificateAnalysis).where(MaterialCertificateAnalysis.id == analysis_record_id)
                    ).scalar_one_or_none()

                    if analysis_record:
                        analysis_record.analysis_status = MaterialCertificateAnalysisStatus.FAILURE
                        session.flush([analysis_record])
            except Exception as db_error:
                logger.error(f"Error updating analysis status to FAILURE: {str(db_error)}")

            logger.error(f"Error processing material certificate analysis: {str(e)}", exc_info=e)
            retry_in = 60 * (2 ** (self.request.retries))
            raise self.retry(exc=e, countdown=retry_in)

    return analyze_material_certificate_agentic  # type: ignore[no-any-return]
