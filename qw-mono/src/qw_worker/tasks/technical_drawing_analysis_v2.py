"""OCR tasks for processing technical drawings."""
import asyncio
import io
import json
import time
from typing import Any, Callable, Dict, List, TypeVar

from celery import Celery, Task
from sqlalchemy import select

from qw_basic_rdb.common import TransactionStrategy, begin_or_use_session
from qw_basic_rdb.interface import RelationalDatabase
from qw_basic_s3.interface import S3Storage
from qw_drawing_toolkit_ocr.drawing_ocr_service import DrawingOcrService, TileInfo
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_monodb.table.trunk.drawing import DrawingFileAnalysis, DrawingFileAnalysisStatus
from qw_monodb.table.trunk.resource import FileResource, FileResourceRevision
from qw_trunk.service.resource.s3_layout import S3BucketLayout
from qw_trunk.service.resource.s3_object import S3ObjectInput, S3ObjectInputData, S3ObjectService
from qw_worker.config import QwWorkerApiKeys

T = TypeVar("T")


def create_technical_drawing_analysis_v2_task(
    app: Celery,
    s3_storage: S3Storage,
    api_keys: QwWorkerApiKeys,
    rdb: RelationalDatabase,
    s3_layout: S3BucketLayout,
    s3_service: S3ObjectService,
    lf: LogFactory = NO_LOG_FACTORY,
) -> Callable[..., Dict[str, Any]]:
    """Create the technical drawing analysis task.

    Args:
        app: The Celery application
        s3_storage: S3 storage instance for accessing files
        api_keys: API keys for external services
        rdb: Relational database instance
        s3_layout: S3 bucket layout
        s3_service: S3 object service
        lf: Log factory to use for logging

    Returns:
        The OCR task function
    """

    @app.task(
        name="qw_worker.tasks.ocr.process_technical_drawing_analysis_v2",
        bind=True,
        max_retries=3,
        soft_time_limit=3600,  # 1 hour timeout
    )  # type: ignore
    def process_technical_drawing_analysis_v2(
        self: Task,
        bucket: str,
        tile_infos: List[Dict[str, Any]],
        file_resource_revision_id: int,
        analysis_record_id: int,
        page_index: int,
    ) -> Dict[str, Any]:
        """
        Process a technical drawing analysis and update status.

        Args:
            self: Task instance (injected by Celery)
            bucket: S3 bucket containing tiles
            tile_infos: List of dictionaries with tile information
            file_resource_revision_id: ID of the file revision being analyzed
            analysis_record_id: ID of the analysis record to update
            page_index: Index of the page being analyzed

        Returns:
            Dictionary with processing status
        """
        logger = lf.get_logger(__name__)
        start_time = time.time()
        logger.info(f"Started analysis for revision {file_resource_revision_id} with {len(tile_infos)} tiles")

        try:
            # Check for deleted files and cancelled analysis
            with begin_or_use_session(rdb, strategy=TransactionStrategy.REUSE_OR_NEW) as session:
                # Check analysis record status first
                analysis_record = session.execute(
                    select(DrawingFileAnalysis).where(DrawingFileAnalysis.id == analysis_record_id)
                ).scalar_one_or_none()

                if not analysis_record:
                    return {"status": "skipped", "reason": "analysis_record_not_found"}

                if analysis_record.analysis_status != DrawingFileAnalysisStatus.PENDING:
                    return {"status": "skipped", "reason": "not_pending"}

                # Check if file revision exists and is not deleted
                file_revision = session.execute(
                    select(FileResourceRevision).where(FileResourceRevision.id == file_resource_revision_id)
                ).scalar_one_or_none()

                if not file_revision:
                    analysis_record.analysis_status = DrawingFileAnalysisStatus.FAILURE
                    session.flush([analysis_record])
                    return {"status": "skipped", "reason": "file_revision_not_found"}

                if file_revision.deleted:
                    analysis_record.analysis_status = DrawingFileAnalysisStatus.FAILURE
                    session.flush([analysis_record])
                    return {"status": "skipped", "reason": "file_revision_deleted"}

            # Process drawing
            tile_info_objects = [TileInfo(**info) for info in tile_infos]
            ocr_service = DrawingOcrService(
                s3_storage=s3_storage,
                google_api_key=api_keys.gemini_api_key,
                openai_api_key=api_keys.openai_key,
                logger=logger,
            )
            result = asyncio.run(ocr_service.process_drawing(bucket=bucket, tile_infos=tile_info_objects))

            # Store results and update database in a single transaction
            with begin_or_use_session(rdb, strategy=TransactionStrategy.REUSE_OR_NEW) as session:
                # Get file resource info
                file_revision = session.execute(
                    select(FileResourceRevision).where(FileResourceRevision.id == file_resource_revision_id)
                ).scalar_one()
                file_resource = session.execute(
                    select(FileResource).where(FileResource.id == file_revision.file_resource_id)
                ).scalar_one()

                # Create S3 path with page index to ensure each page has a unique path
                folder_layout = s3_layout.file_resource(file_resource.uuid, f"v{file_revision.revision_number}")
                path = folder_layout.path_to_drawing_pdf_analysis(version="v2", page_index=page_index)

                # Serialize results
                serializable_results = result.model_dump()
                result_json = json.dumps(serializable_results, indent=2)
                result_bio = io.BytesIO(result_json.encode())

                # Store in S3
                obj_input = S3ObjectInput(
                    bucket=bucket,
                    path=path,
                    data=S3ObjectInputData(
                        bio=result_bio,
                        size=result_bio.getbuffer().nbytes,
                        content_type="application/json",
                    ),
                )
                obj_id = s3_service.add_object(obj_input=obj_input, session=session)
                logger.info(f"Put object '{path}'")

                # Update analysis record in the same transaction
                analysis_record = session.execute(
                    select(DrawingFileAnalysis).where(DrawingFileAnalysis.id == analysis_record_id)
                ).scalar_one_or_none()

                if analysis_record:
                    analysis_record.analysis_obj_id = obj_id
                    analysis_record.analysis_status = DrawingFileAnalysisStatus.COMPLETED
                    session.flush([analysis_record])
                else:
                    logger.warning(
                        f"Analysis record {analysis_record_id} not found, results stored but status not updated"
                    )

            elapsed = time.time() - start_time
            logger.info(f"Total processing completed in {elapsed:.2f}s")

            return {
                "status": "success",
                "processing_time": elapsed,
                "file_resource_revision_id": file_resource_revision_id,
                "analysis_record_id": analysis_record_id,
                "result_size": len(result_json),
            }

        except Exception as e:
            # Update status to FAILURE
            try:
                with begin_or_use_session(rdb, strategy=TransactionStrategy.REUSE_OR_NEW) as session:
                    analysis_record = session.execute(
                        select(DrawingFileAnalysis).where(DrawingFileAnalysis.id == analysis_record_id)
                    ).scalar_one_or_none()

                    if analysis_record:
                        analysis_record.analysis_status = DrawingFileAnalysisStatus.FAILURE
                        session.flush([analysis_record])
            except Exception as db_error:
                logger.error(f"Error updating analysis status to FAILURE: {str(db_error)}")

            logger.error(f"Error processing technical drawing analysis: {str(e)}", exc_info=e)
            retry_in = 60 * (2 ** (self.request.retries))
            raise self.retry(exc=e, countdown=retry_in)

    return process_technical_drawing_analysis_v2  # type: ignore[no-any-return]
