"""Configuration models for Celery worker."""
from typing import Literal

from pydantic import BaseModel

from qw_basic_rdb.builder.base import RelationalDatabaseConfig
from qw_basic_s3.builder import S3StorageConfig
from qw_log.factory import QwLogConfig
from qw_tenant_config.registry import QwTenantsConfig


class QwWorkerApiKeys(BaseModel):
    claude_api_key: str
    groq_api_key: str
    openai_key: str
    gemini_api_key: str
    mistral_api_key: str


class QwCeleryBrokerSettings(BaseModel):
    type: Literal["rabbitmq"]
    host: str
    port: int
    user: str
    password: str


class QwCeleryWorkerSettings(BaseModel):
    concurrency: int
    max_tasks_per_child: int
    loglevel: str


class QwWorkerBaseConfig(BaseModel):
    celery_broker_settings: QwCeleryBrokerSettings
    celery_worker_settings: QwCeleryWorkerSettings
    api_keys: QwWorkerApiKeys


class QwWorkerConfig(QwWorkerBaseConfig):
    """Worker module configuration.

    This class combines all the configuration needed for the worker module.
    """

    s3: S3StorageConfig
    db: RelationalDatabaseConfig
    tenants: QwTenantsConfig


class QwWorkerMonoConfig(BaseModel):
    """Configuration for the worker module in the mono app.

    This class is used to load the configuration from app.yaml.
    """

    mono_worker: QwWorkerBaseConfig
    mono_s3: S3StorageConfig
    mono_db: RelationalDatabaseConfig
    tenants: QwTenantsConfig
    logging: QwLogConfig  # Add logging configuration

    @property
    def worker(self) -> QwWorkerConfig:
        """Get the worker configuration.

        Returns:
            The worker configuration
        """
        return QwWorkerConfig(
            celery_broker_settings=self.mono_worker.celery_broker_settings,
            celery_worker_settings=self.mono_worker.celery_worker_settings,
            api_keys=self.mono_worker.api_keys,
            s3=self.mono_s3,
            tenants=self.tenants,
            db=self.mono_db,
        )
